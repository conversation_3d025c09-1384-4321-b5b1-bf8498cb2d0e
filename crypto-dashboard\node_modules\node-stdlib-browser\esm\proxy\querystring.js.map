{"version": 3, "file": "querystring.js", "sources": ["../../proxy/querystring.js"], "sourcesContent": ["/**\n * @typedef {import('querystring').escape} qsEscape\n * @typedef {import('querystring').unescape} qsUnescape\n */\n\nimport { decode, encode, parse, stringify } from 'querystring-es3';\n\n/**\n * @type {qsEscape}\n */\nfunction qsEscape(string) {\n\treturn encodeURIComponent(string);\n}\n\n/**\n * @type {qsUnescape}\n */\nfunction qsUnescape(string) {\n\treturn decodeURIComponent(string);\n}\n\nconst api = {\n\tdecode,\n\tencode,\n\tparse,\n\tstringify,\n\tescape: qsEscape,\n\tunescape: qsUnescape\n};\n\nexport default api;\n\nexport {\n\tdecode,\n\tencode,\n\tparse,\n\tstringify,\n\tqsEscape as escape,\n\tqsUnescape as unescape\n};\n"], "names": ["qsEscape", "string", "encodeURIComponent", "qsUnescape", "decodeURIComponent", "api", "decode", "encode", "parse", "stringify", "escape", "unescape"], "mappings": ";;;AAAA;AACA;AACA;AACA;;AAIA;AACA;AACA;AACA,SAASA,QAAQA,CAACC,MAAM,EAAE;EACzB,OAAOC,kBAAkB,CAACD,MAAM,CAAC,CAAA;AAClC,CAAA;;AAEA;AACA;AACA;AACA,SAASE,UAAUA,CAACF,MAAM,EAAE;EAC3B,OAAOG,kBAAkB,CAACH,MAAM,CAAC,CAAA;AAClC,CAAA;AAEA,IAAMI,GAAG,GAAG;AACXC,EAAAA,MAAM,EAANA,MAAM;AACNC,EAAAA,MAAM,EAANA,MAAM;AACNC,EAAAA,KAAK,EAALA,KAAK;AACLC,EAAAA,SAAS,EAATA,SAAS;AACTC,EAAAA,MAAM,EAAEV,QAAQ;AAChBW,EAAAA,QAAQ,EAAER,UAAAA;AACX;;;;"}