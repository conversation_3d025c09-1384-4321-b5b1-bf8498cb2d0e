{"version": 3, "file": "index.js", "sources": ["../index.js"], "sourcesContent": ["/* eslint-disable unicorn/custom-error-definition, promise/always-return */\n/* global setImmediate:false, clearImmediate:false */\n\nconst symbolAsyncIterator =\n\tSymbol?.asyncIterator ?? Symbol?.iterator ?? '@@asyncIterator';\n\nclass ERR_INVALID_ARG_TYPE extends Error {\n\tconstructor(name, expected, actual) {\n\t\tsuper(`${name} must be ${expected}, ${typeof actual} given`);\n\t\tthis.code = 'ERR_INVALID_ARG_TYPE';\n\t}\n}\n\nclass AbortError extends Error {\n\tconstructor() {\n\t\tsuper('The operation was aborted');\n\t\tthis.code = 'ABORT_ERR';\n\t}\n}\n\nfunction validateObject(object, name) {\n\tif (object === null || typeof object !== 'object') {\n\t\tthrow new ERR_INVALID_ARG_TYPE(name, 'Object', object);\n\t}\n}\n\nfunction validateBoolean(value, name) {\n\tif (typeof value !== 'boolean') {\n\t\tthrow new ERR_INVALID_ARG_TYPE(name, 'boolean', value);\n\t}\n}\n\nfunction validateAbortSignal(signal, name) {\n\tif (\n\t\ttypeof signal !== 'undefined' &&\n\t\t(signal === null ||\n\t\t\ttypeof signal !== 'object' ||\n\t\t\t!('aborted' in signal))\n\t) {\n\t\tthrow new ERR_INVALID_ARG_TYPE(name, 'AbortSignal', signal);\n\t}\n}\n\nfunction asyncIterator({ next: nextFunction, return: returnFunction }) {\n\tconst result = {};\n\tif (typeof nextFunction === 'function') {\n\t\tresult.next = nextFunction;\n\t}\n\tif (typeof returnFunction === 'function') {\n\t\tresult.return = returnFunction;\n\t}\n\tresult[symbolAsyncIterator] = function () {\n\t\treturn this;\n\t};\n\n\treturn result;\n}\n\nfunction setTimeoutPromise(after = 1, value, options = {}) {\n\tconst arguments_ = [].concat(value ?? []);\n\ttry {\n\t\tvalidateObject(options, 'options');\n\t} catch (error) {\n\t\treturn Promise.reject(error);\n\t}\n\tconst { signal, ref: reference = true } = options;\n\ttry {\n\t\tvalidateAbortSignal(signal, 'options.signal');\n\t} catch (error) {\n\t\treturn Promise.reject(error);\n\t}\n\ttry {\n\t\tvalidateBoolean(reference, 'options.ref');\n\t} catch (error) {\n\t\treturn Promise.reject(error);\n\t}\n\tif (signal?.aborted) {\n\t\treturn Promise.reject(new AbortError());\n\t}\n\tlet onCancel;\n\tconst returnValue = new Promise((resolve, reject) => {\n\t\tconst timeout = setTimeout(() => resolve(value), after, ...arguments_);\n\t\tif (!reference) {\n\t\t\ttimeout?.unref?.();\n\t\t}\n\t\tif (signal) {\n\t\t\tonCancel = () => {\n\t\t\t\tclearTimeout(timeout);\n\t\t\t\treject(new AbortError());\n\t\t\t};\n\t\t\tsignal.addEventListener('abort', onCancel);\n\t\t}\n\t});\n\tif (typeof onCancel !== 'undefined') {\n\t\treturnValue.finally(() =>\n\t\t\tsignal.removeEventListener('abort', onCancel)\n\t\t);\n\t}\n\treturn returnValue;\n}\n\nfunction setImmediatePromise(value, options = {}) {\n\ttry {\n\t\tvalidateObject(options, 'options');\n\t} catch (error) {\n\t\treturn Promise.reject(error);\n\t}\n\tconst { signal, ref: reference = true } = options;\n\ttry {\n\t\tvalidateAbortSignal(signal, 'options.signal');\n\t} catch (error) {\n\t\treturn Promise.reject(error);\n\t}\n\ttry {\n\t\tvalidateBoolean(reference, 'options.ref');\n\t} catch (error) {\n\t\treturn Promise.reject(error);\n\t}\n\tif (signal?.aborted) {\n\t\treturn Promise.reject(new AbortError());\n\t}\n\tlet onCancel;\n\tconst returnValue = new Promise((resolve, reject) => {\n\t\tconst immediate = setImmediate(() => resolve(value));\n\t\tif (!reference) {\n\t\t\timmediate?.unref?.();\n\t\t}\n\t\tif (signal) {\n\t\t\tonCancel = () => {\n\t\t\t\tclearImmediate(immediate);\n\t\t\t\treject(new AbortError());\n\t\t\t};\n\t\t\tsignal.addEventListener('abort', onCancel);\n\t\t}\n\t});\n\tif (typeof onCancel !== 'undefined') {\n\t\treturnValue.finally(() =>\n\t\t\tsignal.removeEventListener('abort', onCancel)\n\t\t);\n\t}\n\treturn returnValue;\n}\n\nfunction setIntervalPromise(after = 1, value, options = {}) {\n\t/* eslint-disable no-undefined, no-unreachable-loop, no-loop-func */\n\ttry {\n\t\tvalidateObject(options, 'options');\n\t} catch (error) {\n\t\treturn asyncIterator({\n\t\t\tnext: function () {\n\t\t\t\treturn Promise.reject(error);\n\t\t\t}\n\t\t});\n\t}\n\tconst { signal, ref: reference = true } = options;\n\ttry {\n\t\tvalidateAbortSignal(signal, 'options.signal');\n\t} catch (error) {\n\t\treturn asyncIterator({\n\t\t\tnext: function () {\n\t\t\t\treturn Promise.reject(error);\n\t\t\t}\n\t\t});\n\t}\n\ttry {\n\t\tvalidateBoolean(reference, 'options.ref');\n\t} catch (error) {\n\t\treturn asyncIterator({\n\t\t\tnext: function () {\n\t\t\t\treturn Promise.reject(error);\n\t\t\t}\n\t\t});\n\t}\n\tif (signal?.aborted) {\n\t\treturn asyncIterator({\n\t\t\tnext: function () {\n\t\t\t\treturn Promise.reject(new AbortError());\n\t\t\t}\n\t\t});\n\t}\n\n\tlet onCancel, interval;\n\n\ttry {\n\t\tlet notYielded = 0;\n\t\tlet callback;\n\t\tinterval = setInterval(() => {\n\t\t\tnotYielded++;\n\t\t\tif (callback) {\n\t\t\t\tcallback();\n\t\t\t\tcallback = undefined;\n\t\t\t}\n\t\t}, after);\n\t\tif (!reference) {\n\t\t\tinterval?.unref?.();\n\t\t}\n\t\tif (signal) {\n\t\t\tonCancel = () => {\n\t\t\t\tclearInterval(interval);\n\t\t\t\tif (callback) {\n\t\t\t\t\tcallback();\n\t\t\t\t\tcallback = undefined;\n\t\t\t\t}\n\t\t\t};\n\t\t\tsignal.addEventListener('abort', onCancel);\n\t\t}\n\n\t\treturn asyncIterator({\n\t\t\tnext: function () {\n\t\t\t\treturn new Promise((resolve, reject) => {\n\t\t\t\t\tif (!signal?.aborted) {\n\t\t\t\t\t\tif (notYielded === 0) {\n\t\t\t\t\t\t\tcallback = resolve;\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\tresolve();\n\t\t\t\t\t\t}\n\t\t\t\t\t} else if (notYielded === 0) {\n\t\t\t\t\t\treject(new AbortError());\n\t\t\t\t\t} else {\n\t\t\t\t\t\tresolve();\n\t\t\t\t\t}\n\t\t\t\t}).then(() => {\n\t\t\t\t\tif (notYielded > 0) {\n\t\t\t\t\t\tnotYielded = notYielded - 1;\n\t\t\t\t\t\treturn { done: false, value: value };\n\t\t\t\t\t}\n\t\t\t\t\treturn { done: true };\n\t\t\t\t});\n\t\t\t},\n\t\t\treturn: function () {\n\t\t\t\tclearInterval(interval);\n\t\t\t\tsignal?.removeEventListener('abort', onCancel);\n\t\t\t\treturn Promise.resolve({});\n\t\t\t}\n\t\t});\n\t} catch (error) {\n\t\treturn asyncIterator({\n\t\t\tnext: function () {\n\t\t\t\tclearInterval(interval);\n\t\t\t\tsignal?.removeEventListener('abort', onCancel);\n\t\t\t}\n\t\t});\n\t}\n}\n\nexport {\n\tsetTimeoutPromise as setTimeout,\n\tsetImmediatePromise as setImmediate,\n\tsetIntervalPromise as setInterval\n};\n"], "names": ["symbolAsyncIterator", "Symbol", "asyncIterator", "iterator", "ERR_INVALID_ARG_TYPE", "name", "expected", "actual", "code", "Error", "AbortError", "validateObject", "object", "validateBoolean", "value", "validateAbortSignal", "signal", "nextFunction", "next", "returnFunction", "result", "setTimeoutPromise", "after", "options", "arguments_", "concat", "error", "Promise", "reject", "ref", "reference", "aborted", "onCancel", "returnValue", "resolve", "timeout", "setTimeout", "unref", "clearTimeout", "addEventListener", "removeEventListener", "setImmediatePromise", "immediate", "setImmediate", "clearImmediate", "setIntervalPromise", "interval", "notYielded", "callback", "setInterval", "undefined", "clearInterval", "then", "done"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;;AACA;AAEA,IAAMA,mBAAmB,oCACxBC,MADwB,oBACxBA,MAAM,CAAEC,aADgB,oCACCD,MADD,oBACCA,MAAM,CAAEE,QADT,mBACqB,iBAD9C;;IAGMC;;;AACL,gCAAYC,IAAZ,EAAkBC,QAAlB,EAA4BC,MAA5B,EAAoC;AAAA;;AAAA,8BAC1BF,IAD0B,iBACVC,QADU,UACG,OAAOC,MADV;AAAA;AAAA,oBAC1BF,IAD0B,iBACVC,QADU,UACG,OAAOC,MADV;;AAAA;AAAA;AAAA;AAAA,8BAC1BF,IAD0B,iBACVC,QADU,UACG,OAAOC,MADV;AAAA;;AAEnC,UAAKC,IAAL,GAAY,sBAAZ;AAFmC;AAGnC;;;iCAJiCC;;IAO7BC;;;AACL,wBAAc;AAAA;;AAAA,gCACP,2BADO;AAAA;AAAA,qBACP,2BADO;;AAAA;AAAA;AAAA;AAAA,+BACP,2BADO;AAAA;;AAEb,WAAKF,IAAL,GAAY,WAAZ;AAFa;AAGb;;;iCAJuBC;;AAOzB,SAASE,cAAT,CAAwBC,MAAxB,EAAgCP,IAAhC,EAAsC;AACrC,MAAIO,MAAM,KAAK,IAAX,IAAmB,OAAOA,MAAP,KAAkB,QAAzC,EAAmD;AAClD,UAAM,IAAIR,oBAAJ,CAAyBC,IAAzB,EAA+B,QAA/B,EAAyCO,MAAzC,CAAN;AACA;AACD;;AAED,SAASC,eAAT,CAAyBC,KAAzB,EAAgCT,IAAhC,EAAsC;AACrC,MAAI,OAAOS,KAAP,KAAiB,SAArB,EAAgC;AAC/B,UAAM,IAAIV,oBAAJ,CAAyBC,IAAzB,EAA+B,SAA/B,EAA0CS,KAA1C,CAAN;AACA;AACD;;AAED,SAASC,mBAAT,CAA6BC,MAA7B,EAAqCX,IAArC,EAA2C;AAC1C,MACC,OAAOW,MAAP,KAAkB,WAAlB,KACCA,MAAM,KAAK,IAAX,IACA,OAAOA,MAAP,KAAkB,QADlB,IAEA,EAAE,aAAaA,MAAf,CAHD,CADD,EAKE;AACD,UAAM,IAAIZ,oBAAJ,CAAyBC,IAAzB,EAA+B,aAA/B,EAA8CW,MAA9C,CAAN;AACA;AACD;;AAED,SAASd,aAAT,QAAuE;AAAA,MAAxCe,YAAwC,SAA9CC,IAA8C;AAAA,MAAlBC,cAAkB;AACtE,MAAMC,MAAM,GAAG,EAAf;;AACA,MAAI,OAAOH,YAAP,KAAwB,UAA5B,EAAwC;AACvCG,IAAAA,MAAM,CAACF,IAAP,GAAcD,YAAd;AACA;;AACD,MAAI,OAAOE,cAAP,KAA0B,UAA9B,EAA0C;AACzCC,IAAAA,MAAM,UAAN,GAAgBD,cAAhB;AACA;;AACDC,EAAAA,MAAM,CAACpB,mBAAD,CAAN,GAA8B,YAAY;AACzC,WAAO,IAAP;AACA,GAFD;;AAIA,SAAOoB,MAAP;AACA;;AAED,SAASC,iBAAT,CAA2BC,KAA3B,EAAsCR,KAAtC,EAA6CS,OAA7C,EAA2D;AAAA,MAAhCD,KAAgC;AAAhCA,IAAAA,KAAgC,GAAxB,CAAwB;AAAA;;AAAA,MAAdC,OAAc;AAAdA,IAAAA,OAAc,GAAJ,EAAI;AAAA;;AAC1D,MAAMC,UAAU,GAAG,GAAGC,MAAH,CAAUX,KAAV,WAAUA,KAAV,GAAmB,EAAnB,CAAnB;;AACA,MAAI;AACHH,IAAAA,cAAc,CAACY,OAAD,EAAU,SAAV,CAAd;AACA,GAFD,CAEE,OAAOG,KAAP,EAAc;AACf,WAAOC,OAAO,CAACC,MAAR,CAAeF,KAAf,CAAP;AACA;;AACD,iBAA0CH,OAA1C;AAAA,MAAQP,MAAR,YAAQA,MAAR;AAAA,8BAAgBa,GAAhB;AAAA,MAAqBC,SAArB,6BAAiC,IAAjC;;AACA,MAAI;AACHf,IAAAA,mBAAmB,CAACC,MAAD,EAAS,gBAAT,CAAnB;AACA,GAFD,CAEE,OAAOU,KAAP,EAAc;AACf,WAAOC,OAAO,CAACC,MAAR,CAAeF,KAAf,CAAP;AACA;;AACD,MAAI;AACHb,IAAAA,eAAe,CAACiB,SAAD,EAAY,aAAZ,CAAf;AACA,GAFD,CAEE,OAAOJ,KAAP,EAAc;AACf,WAAOC,OAAO,CAACC,MAAR,CAAeF,KAAf,CAAP;AACA;;AACD,MAAIV,MAAJ,YAAIA,MAAM,CAAEe,OAAZ,EAAqB;AACpB,WAAOJ,OAAO,CAACC,MAAR,CAAe,IAAIlB,UAAJ,EAAf,CAAP;AACA;;AACD,MAAIsB,QAAJ;AACA,MAAMC,WAAW,GAAG,IAAIN,OAAJ,CAAY,UAACO,OAAD,EAAUN,MAAV,EAAqB;AACpD,QAAMO,OAAO,GAAGC,UAAU,MAAV,UAAW;AAAA,aAAMF,OAAO,CAACpB,KAAD,CAAb;AAAA,KAAX,EAAiCQ,KAAjC,SAA2CE,UAA3C,EAAhB;;AACA,QAAI,CAACM,SAAL,EAAgB;AACfK,MAAAA,OAAO,QAAP,YAAAA,OAAO,CAAEE,KAAT,oBAAAF,OAAO,CAAEE,KAAT;AACA;;AACD,QAAIrB,MAAJ,EAAY;AACXgB,MAAAA,QAAQ,GAAG,oBAAM;AAChBM,QAAAA,YAAY,CAACH,OAAD,CAAZ;AACAP,QAAAA,MAAM,CAAC,IAAIlB,UAAJ,EAAD,CAAN;AACA,OAHD;;AAIAM,MAAAA,MAAM,CAACuB,gBAAP,CAAwB,OAAxB,EAAiCP,QAAjC;AACA;AACD,GAZmB,CAApB;;AAaA,MAAI,OAAOA,QAAP,KAAoB,WAAxB,EAAqC;AACpCC,IAAAA,WAAW,WAAX,CAAoB;AAAA,aACnBjB,MAAM,CAACwB,mBAAP,CAA2B,OAA3B,EAAoCR,QAApC,CADmB;AAAA,KAApB;AAGA;;AACD,SAAOC,WAAP;AACA;;AAED,SAASQ,mBAAT,CAA6B3B,KAA7B,EAAoCS,OAApC,EAAkD;AAAA,MAAdA,OAAc;AAAdA,IAAAA,OAAc,GAAJ,EAAI;AAAA;;AACjD,MAAI;AACHZ,IAAAA,cAAc,CAACY,OAAD,EAAU,SAAV,CAAd;AACA,GAFD,CAEE,OAAOG,KAAP,EAAc;AACf,WAAOC,OAAO,CAACC,MAAR,CAAeF,KAAf,CAAP;AACA;;AACD,kBAA0CH,OAA1C;AAAA,MAAQP,MAAR,aAAQA,MAAR;AAAA,gCAAgBa,GAAhB;AAAA,MAAqBC,SAArB,8BAAiC,IAAjC;;AACA,MAAI;AACHf,IAAAA,mBAAmB,CAACC,MAAD,EAAS,gBAAT,CAAnB;AACA,GAFD,CAEE,OAAOU,KAAP,EAAc;AACf,WAAOC,OAAO,CAACC,MAAR,CAAeF,KAAf,CAAP;AACA;;AACD,MAAI;AACHb,IAAAA,eAAe,CAACiB,SAAD,EAAY,aAAZ,CAAf;AACA,GAFD,CAEE,OAAOJ,KAAP,EAAc;AACf,WAAOC,OAAO,CAACC,MAAR,CAAeF,KAAf,CAAP;AACA;;AACD,MAAIV,MAAJ,YAAIA,MAAM,CAAEe,OAAZ,EAAqB;AACpB,WAAOJ,OAAO,CAACC,MAAR,CAAe,IAAIlB,UAAJ,EAAf,CAAP;AACA;;AACD,MAAIsB,QAAJ;AACA,MAAMC,WAAW,GAAG,IAAIN,OAAJ,CAAY,UAACO,OAAD,EAAUN,MAAV,EAAqB;AACpD,QAAMc,SAAS,GAAGC,YAAY,CAAC;AAAA,aAAMT,OAAO,CAACpB,KAAD,CAAb;AAAA,KAAD,CAA9B;;AACA,QAAI,CAACgB,SAAL,EAAgB;AACfY,MAAAA,SAAS,QAAT,YAAAA,SAAS,CAAEL,KAAX,oBAAAK,SAAS,CAAEL,KAAX;AACA;;AACD,QAAIrB,MAAJ,EAAY;AACXgB,MAAAA,QAAQ,GAAG,oBAAM;AAChBY,QAAAA,cAAc,CAACF,SAAD,CAAd;AACAd,QAAAA,MAAM,CAAC,IAAIlB,UAAJ,EAAD,CAAN;AACA,OAHD;;AAIAM,MAAAA,MAAM,CAACuB,gBAAP,CAAwB,OAAxB,EAAiCP,QAAjC;AACA;AACD,GAZmB,CAApB;;AAaA,MAAI,OAAOA,QAAP,KAAoB,WAAxB,EAAqC;AACpCC,IAAAA,WAAW,WAAX,CAAoB;AAAA,aACnBjB,MAAM,CAACwB,mBAAP,CAA2B,OAA3B,EAAoCR,QAApC,CADmB;AAAA,KAApB;AAGA;;AACD,SAAOC,WAAP;AACA;;AAED,SAASY,kBAAT,CAA4BvB,KAA5B,EAAuCR,KAAvC,EAA8CS,OAA9C,EAA4D;AAAA,MAAhCD,KAAgC;AAAhCA,IAAAA,KAAgC,GAAxB,CAAwB;AAAA;;AAAA,MAAdC,OAAc;AAAdA,IAAAA,OAAc,GAAJ,EAAI;AAAA;;AAC3D;AACA,MAAI;AACHZ,IAAAA,cAAc,CAACY,OAAD,EAAU,SAAV,CAAd;AACA,GAFD,CAEE,OAAOG,KAAP,EAAc;AACf,WAAOxB,aAAa,CAAC;AACpBgB,MAAAA,IAAI,EAAE,gBAAY;AACjB,eAAOS,OAAO,CAACC,MAAR,CAAeF,KAAf,CAAP;AACA;AAHmB,KAAD,CAApB;AAKA;;AACD,kBAA0CH,OAA1C;AAAA,MAAQP,MAAR,aAAQA,MAAR;AAAA,gCAAgBa,GAAhB;AAAA,MAAqBC,SAArB,8BAAiC,IAAjC;;AACA,MAAI;AACHf,IAAAA,mBAAmB,CAACC,MAAD,EAAS,gBAAT,CAAnB;AACA,GAFD,CAEE,OAAOU,KAAP,EAAc;AACf,WAAOxB,aAAa,CAAC;AACpBgB,MAAAA,IAAI,EAAE,gBAAY;AACjB,eAAOS,OAAO,CAACC,MAAR,CAAeF,KAAf,CAAP;AACA;AAHmB,KAAD,CAApB;AAKA;;AACD,MAAI;AACHb,IAAAA,eAAe,CAACiB,SAAD,EAAY,aAAZ,CAAf;AACA,GAFD,CAEE,OAAOJ,KAAP,EAAc;AACf,WAAOxB,aAAa,CAAC;AACpBgB,MAAAA,IAAI,EAAE,gBAAY;AACjB,eAAOS,OAAO,CAACC,MAAR,CAAeF,KAAf,CAAP;AACA;AAHmB,KAAD,CAApB;AAKA;;AACD,MAAIV,MAAJ,YAAIA,MAAM,CAAEe,OAAZ,EAAqB;AACpB,WAAO7B,aAAa,CAAC;AACpBgB,MAAAA,IAAI,EAAE,gBAAY;AACjB,eAAOS,OAAO,CAACC,MAAR,CAAe,IAAIlB,UAAJ,EAAf,CAAP;AACA;AAHmB,KAAD,CAApB;AAKA;;AAED,MAAIsB,QAAJ,EAAcc,QAAd;;AAEA,MAAI;AACH,QAAIC,UAAU,GAAG,CAAjB;AACA,QAAIC,QAAJ;AACAF,IAAAA,QAAQ,GAAGG,WAAW,CAAC,YAAM;AAC5BF,MAAAA,UAAU;;AACV,UAAIC,QAAJ,EAAc;AACbA,QAAAA,QAAQ;AACRA,QAAAA,QAAQ,GAAGE,SAAX;AACA;AACD,KANqB,EAMnB5B,KANmB,CAAtB;;AAOA,QAAI,CAACQ,SAAL,EAAgB;AAAA;;AACf,mBAAAgB,QAAQ,SAAR,sBAAUT,KAAV,8BAAUA,KAAV;AACA;;AACD,QAAIrB,MAAJ,EAAY;AACXgB,MAAAA,QAAQ,GAAG,oBAAM;AAChBmB,QAAAA,aAAa,CAACL,QAAD,CAAb;;AACA,YAAIE,QAAJ,EAAc;AACbA,UAAAA,QAAQ;AACRA,UAAAA,QAAQ,GAAGE,SAAX;AACA;AACD,OAND;;AAOAlC,MAAAA,MAAM,CAACuB,gBAAP,CAAwB,OAAxB,EAAiCP,QAAjC;AACA;;AAED,WAAO9B,aAAa,CAAC;AACpBgB,MAAAA,IAAI,EAAE,gBAAY;AACjB,eAAO,IAAIS,OAAJ,CAAY,UAACO,OAAD,EAAUN,MAAV,EAAqB;AACvC,cAAI,EAACZ,MAAD,YAACA,MAAM,CAAEe,OAAT,CAAJ,EAAsB;AACrB,gBAAIgB,UAAU,KAAK,CAAnB,EAAsB;AACrBC,cAAAA,QAAQ,GAAGd,OAAX;AACA,aAFD,MAEO;AACNA,cAAAA,OAAO;AACP;AACD,WAND,MAMO,IAAIa,UAAU,KAAK,CAAnB,EAAsB;AAC5BnB,YAAAA,MAAM,CAAC,IAAIlB,UAAJ,EAAD,CAAN;AACA,WAFM,MAEA;AACNwB,YAAAA,OAAO;AACP;AACD,SAZM,EAYJkB,IAZI,CAYC,YAAM;AACb,cAAIL,UAAU,GAAG,CAAjB,EAAoB;AACnBA,YAAAA,UAAU,GAAGA,UAAU,GAAG,CAA1B;AACA,mBAAO;AAAEM,cAAAA,IAAI,EAAE,KAAR;AAAevC,cAAAA,KAAK,EAAEA;AAAtB,aAAP;AACA;;AACD,iBAAO;AAAEuC,YAAAA,IAAI,EAAE;AAAR,WAAP;AACA,SAlBM,CAAP;AAmBA,OArBmB;AAsBpB,gBAAQ,mBAAY;AACnBF,QAAAA,aAAa,CAACL,QAAD,CAAb;AACA9B,QAAAA,MAAM,QAAN,YAAAA,MAAM,CAAEwB,mBAAR,CAA4B,OAA5B,EAAqCR,QAArC;AACA,eAAOL,OAAO,CAACO,OAAR,CAAgB,EAAhB,CAAP;AACA;AA1BmB,KAAD,CAApB;AA4BA,GApDD,CAoDE,OAAOR,KAAP,EAAc;AACf,WAAOxB,aAAa,CAAC;AACpBgB,MAAAA,IAAI,EAAE,gBAAY;AACjBiC,QAAAA,aAAa,CAACL,QAAD,CAAb;AACA9B,QAAAA,MAAM,QAAN,YAAAA,MAAM,CAAEwB,mBAAR,CAA4B,OAA5B,EAAqCR,QAArC;AACA;AAJmB,KAAD,CAApB;AAMA;AACD;;;;"}