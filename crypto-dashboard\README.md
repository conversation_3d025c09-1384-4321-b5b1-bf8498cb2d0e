# 数字货币展示系统

一个基于 Vue 3 + TypeScript + Vite 的数字货币展示系统，展示各大交易所的基础信息和交易对数据。

## 功能特性

### 🏢 交易所管理
- **完整交易所列表**：显示CCXT支持的所有主要交易所（15+个）及其基础信息
- **Logo点击跳转**：点击交易所Logo可直接跳转到官网
- **24h交易量显示**：展示各交易所的24小时交易量
- **多维度排序**：支持按名称、交易量、国家数量、请求限制等排序
- **类型筛选**：可按现货、期货、保证金、期权等交易类型筛选
- **搜索功能**：支持按交易所名称、ID、国家等关键词搜索
- **全局排序入口**：可直接跳转到按USDT交易量排序的全局交易对页面

### 💰 交易对管理
- **全局交易对列表**：展示所有交易所的交易对信息
- **单交易所交易对**：可查看特定交易所的所有交易对
- **实时行情数据**：显示最新价格、24h涨跌幅、成交量等
- **交易所Logo显示**：每个交易对都显示对应交易所的Logo
- **USDT等值交易量**：自动换算各种货币对的24h交易量为USDT等值
- **智能筛选**：支持按交易类型、活跃状态筛选
- **多维度排序**：支持按价格、涨跌幅、原始成交量、USDT等值交易量等排序
- **全局USDT排序**：一键按所有市场的USDT等值交易量进行排序

### 🎨 用户界面
- **现代化设计**：采用渐变背景和毛玻璃效果
- **响应式布局**：适配不同屏幕尺寸
- **直观的数据展示**：使用表格、标签、统计卡片等组件
- **流畅的交互体验**：加载状态、错误处理、分页等

## 技术栈

- **前端框架**：Vue 3 (Composition API)
- **开发语言**：TypeScript
- **构建工具**：Vite
- **UI组件库**：Element Plus
- **状态管理**：Pinia
- **路由管理**：Vue Router 4
- **数据源**：模拟CCXT数据（演示用）

## 项目结构

```
src/
├── components/          # 可复用组件
├── views/              # 页面组件
│   ├── ExchangesView.vue       # 交易所列表页
│   ├── SymbolsView.vue         # 全局交易对页
│   └── ExchangeSymbolsView.vue # 单交易所交易对页
├── stores/             # Pinia状态管理
│   ├── index.ts
│   └── exchangeStore.ts
├── services/           # API服务层
│   └── mockCcxtService.ts
├── types/              # TypeScript类型定义
│   └── index.ts
├── router/             # 路由配置
│   └── index.ts
└── utils/              # 工具函数
```

## 快速开始

### 安装依赖
```bash
npm install
```

### 启动开发服务器
```bash
npm run dev
```

### 构建生产版本
```bash
npm run build
```

## 页面说明

### 1. 交易所列表页 (`/exchanges`)
- 展示所有支持的交易所
- 显示交易所基本信息：名称、支持国家、交易类型、请求限制等
- 支持多种筛选和排序方式
- 点击"查看交易对"可跳转到该交易所的交易对页面

### 2. 全局交易对页 (`/symbols`)
- 展示所有交易所的交易对
- 显示实时行情数据
- 支持按交易所、交易类型筛选
- 分页显示，提高性能

### 3. 单交易所交易对页 (`/exchange/:id/symbols`)
- 展示特定交易所的所有交易对
- 显示更详细的交易对信息（精度、最小交易量等）
- 提供返回按钮和面包屑导航

## 数据说明

本项目支持两种数据源模式：

### 🔄 混合数据模式（当前）
- **智能切换**：自动检测CCXT库可用性，优先使用真实数据，失败时回退到模拟数据
- **浏览器CCXT**：应用启动时会尝试加载CCXT到 `window.ccxt`，可在控制台直接使用
- **优雅降级**：确保应用在任何环境下都能正常运行

### 📊 模拟数据特性
- **15+个主流交易所**：Binance、OKX、Bybit、Coinbase Pro、Kraken、Huobi、KuCoin、Gate.io、Bitfinex、BitMEX、Bitget、MEXC、Phemex、BitMart、Crypto.com、Gemini、Bitstamp等
- **多种交易对**：BTC、ETH、BNB、ADA、DOT、LINK、UNI、LTC、XRP、SOL等主流币种与USDT、USD、EUR等法币的组合
- **实时行情模拟**：随机生成的价格、涨跌幅、成交量等数据
- **货币汇率转换**：内置汇率转换器，支持将各种货币的交易量转换为USDT等值
- **Logo和官网链接**：每个交易所都配置了官方Logo和网站链接

### 🔧 CCXT集成
详细的CCXT集成说明请参考 [CCXT_INTEGRATION.md](./CCXT_INTEGRATION.md) 文件，包括：
- 浏览器中使用CCXT的方法
- 生产环境部署建议
- 故障排除和调试技巧

### 🧪 CCXT测试页面
为了方便测试CCXT功能，我们提供了专门的测试页面：

- **完整测试页面**: http://localhost:5173/ccxt-test.html
  - 全面的CCXT功能测试
  - 可视化交易所列表
  - 实时数据获取测试
  - 结果导出功能

- **简化测试页面**: http://localhost:5173/ccxt-simple-test.html
  - 快速验证CCXT可用性
  - 核心功能测试
  - 轻量级设计

详细使用说明请参考 [CCXT_TEST_GUIDE.md](./CCXT_TEST_GUIDE.md)

## 扩展说明

在实际生产环境中，可以：

1. **替换数据源**：将模拟服务替换为真实的CCXT服务或其他API
2. **添加更多功能**：K线图、深度图、交易功能等
3. **优化性能**：虚拟滚动、数据缓存、懒加载等
4. **增强安全性**：API密钥管理、CORS配置等

## 许可证

MIT License

## Recommended IDE Setup

[VSCode](https://code.visualstudio.com/) + [Volar](https://marketplace.visualstudio.com/items?itemName=Vue.volar) (and disable Vetur).

## Type Support for `.vue` Imports in TS

TypeScript cannot handle type information for `.vue` imports by default, so we replace the `tsc` CLI with `vue-tsc` for type checking. In editors, we need [Volar](https://marketplace.visualstudio.com/items?itemName=Vue.volar) to make the TypeScript language service aware of `.vue` types.

## Customize configuration

See [Vite Configuration Reference](https://vite.dev/config/).

## Project Setup

```sh
npm install
```

### Compile and Hot-Reload for Development

```sh
npm run dev
```

### Type-Check, Compile and Minify for Production

```sh
npm run build
```
