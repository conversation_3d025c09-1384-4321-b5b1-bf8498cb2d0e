{"name": "vite-plugin-node-polyfills", "type": "module", "version": "0.24.0", "description": "A Vite plugin to polyfill Node's Core Modules for browser environments.", "author": "<PERSON> <<EMAIL>>", "license": "MIT", "funding": "https://github.com/sponsors/davidmyersdev", "homepage": "https://github.com/davidmyersdev/vite-plugin-node-polyfills", "repository": {"type": "git", "url": "git+https://github.com/davidmyersdev/vite-plugin-node-polyfills.git"}, "keywords": ["node", "node-core-modules", "node-polyfills", "node-stdlib", "polyfills", "vite", "vite-plugin"], "exports": {".": {"types": "./dist/index.d.ts", "require": "./dist/index.cjs", "import": "./dist/index.js"}, "./shims": {"require": "./shims/dist/index.cjs", "import": "./shims/dist/index.js"}, "./shims/buffer": {"require": "./shims/buffer/dist/index.cjs", "import": "./shims/buffer/dist/index.js"}, "./shims/buffer/": {"require": "./shims/buffer/dist/index.cjs", "import": "./shims/buffer/dist/index.js"}, "./shims/global": {"require": "./shims/global/dist/index.cjs", "import": "./shims/global/dist/index.js"}, "./shims/global/": {"require": "./shims/global/dist/index.cjs", "import": "./shims/global/dist/index.js"}, "./shims/process": {"require": "./shims/process/dist/index.cjs", "import": "./shims/process/dist/index.js"}, "./shims/process/": {"require": "./shims/process/dist/index.cjs", "import": "./shims/process/dist/index.js"}}, "main": "./dist/index.cjs", "module": "./dist/index.js", "types": "./dist/index.d.ts", "files": ["dist", "shims"], "peerDependencies": {"vite": "^2.0.0 || ^3.0.0 || ^4.0.0 || ^5.0.0 || ^6.0.0 || ^7.0.0"}, "dependencies": {"@rollup/plugin-inject": "^5.0.5", "node-stdlib-browser": "^1.2.0"}, "devDependencies": {"@playwright/test": "^1.40.1", "@types/node": "^18.18.8", "buffer": "6.0.3", "esbuild": "^0.19.8", "eslint": "^8.54.0", "eslint-config-artisan": "^0.2.1", "npm-run-all": "^4.1.5", "process": "^0.11.10", "rollup": "^4.6.0", "typescript": "4.8.3", "vite": "^5.0.2", "vite-node": "^0.34.6", "vite-plugin-externalize-deps": "^0.1.5", "vite-plugin-inspect": "^0.7.42", "vitest": "^1.2.2", "vite-plugin-node-polyfills": "0.24.0"}, "publishConfig": {"access": "public"}, "scripts": {"build": "run-s build:core build:shims build:types", "build:core": "vite build", "build:shims": "run-p build:shims:*", "build:shims:buffer": "vite build -c ./shims/vite.config.ts ./shims/buffer", "build:shims:global": "vite build -c ./shims/vite.config.ts ./shims/global", "build:shims:process": "vite build -c ./shims/vite.config.ts ./shims/process", "build:types": "run-s typecheck", "lint": "eslint .", "lint:fix": "eslint --fix .", "test": "run-p test:build test:error-repros test:unit", "test:build": "run-p test:build:*", "test:build:react": "pnpm -C ./examples/react run build", "test:build:vanilla": "pnpm -C ./examples/vanilla run build", "test:build:vue": "pnpm -C ./examples/vue run build", "test:dev:react": "pnpm -C ./examples/react run dev", "test:dev:vanilla": "pnpm -C ./examples/vanilla run dev", "test:dev:vue": "pnpm -C ./examples/vue run dev", "test:error-repros": "run-p test:error-repros:*", "test:error-repros:vite-scan-buffer-import-error": "pnpm --filter vite-scan-buffer-import-error test", "test:unit": "vitest run --dir ./test", "typecheck": "run-p typecheck:*", "typecheck:core": "tsc", "typecheck:react": "pnpm -C ./examples/react run typecheck", "typecheck:vanilla": "pnpm -C ./examples/vanilla run typecheck", "typecheck:vue": "pnpm -C ./examples/vue run typecheck"}}