"use strict";Object.defineProperty(exports,Symbol.toStringTag,{value:"Module"});const T=require("node:module"),x=require("@rollup/plugin-inject"),O=require("node-stdlib-browser"),P=require("node-stdlib-browser/helpers/rollup/plugin"),R=require("node-stdlib-browser/helpers/esbuild/plugin");var h=typeof document<"u"?document.currentScript:null;const g=l=>l&&l.__esModule?l:{default:l},S=g(x),q=g(O),w=g(R),_=(l,e)=>b(l)===b(e),s=(l,e)=>l?l===!0?!0:l===e:!1,$=l=>l.startsWith("node:"),I=l=>{const e=l.replace(/[.*+?^${}()|[\]\\]/g,"\\$&");return new RegExp(`^${e}$`)},b=l=>l.replace(/^node:/,""),a={buffer:["import __buffer_polyfill from 'vite-plugin-node-polyfills/shims/buffer'","globalThis.Buffer = globalThis.Buffer || __buffer_polyfill"],global:["import __global_polyfill from 'vite-plugin-node-polyfills/shims/global'","globalThis.global = globalThis.global || __global_polyfill"],process:["import __process_polyfill from 'vite-plugin-node-polyfills/shims/process'","globalThis.process = globalThis.process || __process_polyfill"]},D=(l={})=>{const e={include:[],exclude:[],overrides:{},protocolImports:!0,...l,globals:{Buffer:!0,global:!0,process:!0,...l.globals}},y=o=>e.include.length>0?!e.include.some(r=>_(o,r)):e.exclude.some(r=>_(o,r)),B=o=>{if(s(e.globals.Buffer,"dev")&&/^buffer$/.test(o))return"vite-plugin-node-polyfills/shims/buffer";if(s(e.globals.global,"dev")&&/^global$/.test(o))return"vite-plugin-node-polyfills/shims/global";if(s(e.globals.process,"dev")&&/^process$/.test(o))return"vite-plugin-node-polyfills/shims/process";if(o in e.overrides)return e.overrides[o]},u=Object.entries(q.default).reduce((o,[r,i])=>(!e.protocolImports&&$(r)||y(r)||(o[r]=B(b(r))||i),o),{}),f=T.createRequire(typeof document>"u"?require("url").pathToFileURL(__filename).href:h&&h.src||new URL("index.cjs",document.baseURI).href),p=[...s(e.globals.Buffer,"dev")?[f.resolve("vite-plugin-node-polyfills/shims/buffer")]:[],...s(e.globals.global,"dev")?[f.resolve("vite-plugin-node-polyfills/shims/global")]:[],...s(e.globals.process,"dev")?[f.resolve("vite-plugin-node-polyfills/shims/process")]:[]],d=[...s(e.globals.Buffer,"dev")?a.buffer:[],...s(e.globals.global,"dev")?a.global:[],...s(e.globals.process,"dev")?a.process:[],""].join(`
`);return{name:"vite-plugin-node-polyfills",config(o,r){const i=r.command==="serve",v=!!this?.meta?.rolldownVersion,m={...i&&s(e.globals.Buffer,"dev")?{Buffer:"Buffer"}:{},...i&&s(e.globals.global,"dev")?{global:"global"}:{},...i&&s(e.globals.process,"dev")?{process:"process"}:{}},c={...s(e.globals.Buffer,"build")?{Buffer:"vite-plugin-node-polyfills/shims/buffer"}:{},...s(e.globals.global,"build")?{global:"vite-plugin-node-polyfills/shims/global"}:{},...s(e.globals.process,"build")?{process:"vite-plugin-node-polyfills/shims/process"}:{}};return{build:{rollupOptions:{onwarn:(t,n)=>{P.handleCircularDependancyWarning(t,()=>{if(o.build?.rollupOptions?.onwarn)return o.build.rollupOptions.onwarn(t,n);n(t)})},...Object.keys(c).length>0?v?{inject:c}:{plugins:[S.default(c)]}:{}}},esbuild:{banner:i?d:void 0},optimizeDeps:{exclude:[...p],...v?{rollupOptions:{define:m,resolve:{alias:{...u}},plugins:[{name:"vite-plugin-node-polyfills:optimizer",banner:i?d:void 0}]}}:{esbuildOptions:{banner:i?{js:d}:void 0,define:m,inject:[...p],plugins:[w.default(u),{name:"vite-plugin-node-polyfills-shims-resolver",setup(t){for(const n of p){const j=I(n);t.onResolve({filter:j},()=>({external:!1,path:n}))}}}]}}},resolve:{alias:{...u}}}}}};exports.nodePolyfills=D;
//# sourceMappingURL=index.cjs.map
