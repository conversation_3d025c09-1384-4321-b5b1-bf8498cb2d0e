{"version": 3, "file": "buffer.js", "sources": ["../../mock/buffer.js"], "sourcesContent": ["function Buffer() {\n\tthrow new Error('Buffer is not included.');\n}\nBuffer.isBuffer = function () {\n\treturn false;\n};\n\nconst INSPECT_MAX_BYTES = 50;\n\nexport { INSPECT_MAX_BYTES, Buffer as <PERSON>Buffer, Buffer };\n"], "names": ["<PERSON><PERSON><PERSON>", "Error", "<PERSON><PERSON><PERSON><PERSON>", "INSPECT_MAX_BYTES"], "mappings": "AAAA,SAASA,MAAMA,GAAG;AACjB,EAAA,MAAM,IAAIC,KAAK,CAAC,yBAAyB,CAAC,CAAA;AAC3C,CAAA;AACAD,MAAM,CAACE,QAAQ,GAAG,YAAY;AAC7B,EAAA,OAAO,KAAK,CAAA;AACb,CAAC,CAAA;AAEKC,IAAAA,iBAAiB,GAAG;;;;"}