export default api;
declare namespace api {
    export { nextTick };
    export { title };
    export { browser };
    export { environment as env };
    export { argv };
    export { version };
    export { versions };
    export { on };
    export { addListener };
    export { once };
    export { off };
    export { removeListener };
    export { removeAllListeners };
    export { emit };
    export { emitWarning };
    export { prependListener };
    export { prependOnceListener };
    export { listeners };
    export { binding };
    export { cwd };
    export { chdir };
    export { umask };
    export { exit };
    export { pid };
    export { features };
    export { kill };
    export { dlopen };
    export { uptime };
    export { memoryUsage };
    export { uvCounters };
    export { platform };
    export { arch };
    export { execPath };
    export { execArgv };
}
import { nextTick } from "node/process";
import { title } from "node/process";
export const browser: boolean;
import { env as environment } from "node/process";
import { argv } from "node/process";
import { version } from "node/process";
import { versions } from "node/process";
import { on } from "node/process";
import { addListener } from "node/process";
import { once } from "node/process";
import { off } from "node/events";
import { removeListener } from "node/events";
import { removeAllListeners } from "node/events";
import { emit } from "node/process";
export function emitWarning(): void;
import { prependListener } from "node/process";
import { prependOnceListener } from "node/process";
import { listeners } from "node/process";
export const binding: Function;
import { cwd } from "node/process";
import { chdir } from "node/process";
import { umask } from "node/process";
export function exit(): void;
export const pid: 1;
export const features: {};
export function kill(): void;
export function dlopen(): void;
export function uptime(): void;
export function memoryUsage(): void;
export function uvCounters(): void;
export const platform: "browser";
export const arch: "browser";
export const execPath: "browser";
export const execArgv: string[];
export { nextTick, title, environment as env, argv, version, versions, on, addListener, once, off, removeListener, removeAllListeners, emit, prependListener, prependOnceListener, listeners, cwd, chdir, umask };
//# sourceMappingURL=process.d.ts.map