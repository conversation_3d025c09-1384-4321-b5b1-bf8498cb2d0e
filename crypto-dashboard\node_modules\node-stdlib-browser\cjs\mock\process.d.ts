export const features: {};
/**
 * @param {unknown[]} arguments_
 */
export function nextTick(...arguments_: unknown[]): void;
export const pid: 1;
export const browser: true;
declare const environment: {};
/** @type {string[]} */
export const argv: string[];
/**
 * @param {unknown} name
 */
export function binding(name: unknown): void;
declare function getCwd(): string;
/**
 * @param {string} dir
 */
declare function getChdir(dir: string): void;
declare function noop(): void;
declare const platformName: "browser";
export { environment as env, getCwd as cwd, getChdir as chdir, noop as exit, noop as kill, noop as umask, noop as dlopen, noop as uptime, noop as memoryUsage, noop as uvCounters, platformName as platform, platformName as arch, platformName as execPath, platformName as title };
//# sourceMappingURL=process.d.ts.map