{"version": 3, "file": "console.js", "sources": ["../../mock/console.js"], "sourcesContent": ["const _console = globalThis.console ?? {};\n\nconst consoleApi = {\n\tlog: 1,\n\tinfo: 1,\n\terror: 1,\n\twarn: 1,\n\tdir: 1,\n\ttrace: 1,\n\tassert: 1,\n\ttime: 1,\n\ttimeEnd: 1\n};\n\n/** @typedef {keyof consoleApi} ConsoleApi */\n\nfor (const property in consoleApi) {\n\tif (!_console[/** @type {ConsoleApi} */ (property)]) {\n\t\t_console[/** @type {ConsoleApi} */ (property)] = function () {};\n\t}\n}\n\nexport default _console;\n"], "names": ["_console", "_globalThis$console", "_globalThis", "console", "consoleApi", "log", "info", "error", "warn", "dir", "trace", "assert", "time", "timeEnd", "property"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;AAAMA,IAAAA,QAAQ,GAAAC,CAAAA,mBAAA,GAAGC,WAAA,CAAWC,OAAO,KAAAF,IAAAA,GAAAA,mBAAA,GAAI,GAAE;AAEzC,IAAMG,UAAU,GAAG;AAClBC,EAAAA,GAAG,EAAE,CAAC;AACNC,EAAAA,IAAI,EAAE,CAAC;AACPC,EAAAA,KAAK,EAAE,CAAC;AACRC,EAAAA,IAAI,EAAE,CAAC;AACPC,EAAAA,GAAG,EAAE,CAAC;AACNC,EAAAA,KAAK,EAAE,CAAC;AACRC,EAAAA,MAAM,EAAE,CAAC;AACTC,EAAAA,IAAI,EAAE,CAAC;AACPC,EAAAA,OAAO,EAAE,CAAA;AACV,CAAC,CAAA;;AAED;;AAEA,KAAK,IAAMC,QAAQ,IAAIV,UAAU,EAAE;AAClC,EAAA,IAAI,CAACJ,QAAQ,2BAA4Bc,QAAQ,EAAE,EAAE;AACpDd,IAAAA,QAAQ,2BAA4Bc,QAAQ,EAAE,GAAG,YAAY,EAAE,CAAA;AAChE,GAAA;AACD;;;;"}