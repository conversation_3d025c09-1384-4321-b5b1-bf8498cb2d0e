{"version": 3, "file": "punycode.js", "sources": ["../../mock/punycode.js"], "sourcesContent": ["/**\n * @param {string} s\n */\nfunction passthrough(s) {\n\treturn s;\n}\n\nconst ucs2 = {\n\tencode: passthrough,\n\tdecode: passthrough\n};\n\nconst version = '0.0.0';\n\nexport {\n\tucs2,\n\tversion,\n\tpassthrough as encode,\n\tpassthrough as decode,\n\tpassthrough as toUnicode,\n\tpassthrough as toASCII\n};\n"], "names": ["passthrough", "s", "ucs2", "encode", "decode", "version"], "mappings": "AAAA;AACA;AACA;AACA,SAASA,WAAWA,CAACC,CAAC,EAAE;AACvB,EAAA,OAAOA,CAAC,CAAA;AACT,CAAA;AAEA,IAAMC,IAAI,GAAG;AACZC,EAAAA,MAAM,EAAEH,WAAW;AACnBI,EAAAA,MAAM,EAAEJ,WAAAA;AACT,EAAC;AAEKK,IAAAA,OAAO,GAAG;;;;"}