{"version": 3, "file": "index.js", "sources": ["../index.js"], "sourcesContent": ["import createRequire from 'create-require';\nimport pkgDir from 'pkg-dir';\n\n/**\n * @param {string} path\n */\nconst resolvePath = (path) => {\n\tlet resolvedPath;\n\ttry {\n\t\tresolvedPath = require.resolve(path);\n\t} catch {\n\t\tresolvedPath = (\n\t\t\tglobalThis.require ?? createRequire(import.meta.url)\n\t\t).resolve(path);\n\t}\n\tif (!path.includes('./')) {\n\t\tconst directory = pkgDir.sync(resolvedPath) ?? '';\n\t\treturn directory;\n\t}\n\treturn resolvedPath;\n};\n\nconst assert = resolvePath('assert/');\nconst buffer = resolvePath('buffer/');\nconst child_process = resolvePath('./mock/empty.js');\nconst cluster = resolvePath('./mock/empty.js');\nconst _console = resolvePath('console-browserify');\nconst constants = resolvePath('constants-browserify');\nconst crypto = resolvePath('crypto-browserify');\nconst dgram = resolvePath('./mock/empty.js');\nconst dns = resolvePath('./mock/empty.js');\nconst domain = resolvePath('domain-browser');\nconst events = resolvePath('events/');\nconst fs = resolvePath('./mock/empty.js');\nconst http = resolvePath('stream-http');\nconst https = resolvePath('https-browserify');\nconst http2 = resolvePath('./mock/empty.js');\nconst _module = resolvePath('./mock/empty.js');\nconst net = resolvePath('./mock/empty.js');\nconst os = resolvePath('os-browserify/browser.js');\nconst path = resolvePath('path-browserify');\nconst punycode = resolvePath('punycode/');\nconst _process = resolvePath('./proxy/process').replace('.js', '');\nconst querystring = resolvePath('./proxy/querystring.js');\nconst readline = resolvePath('./mock/empty.js');\nconst repl = resolvePath('./mock/empty.js');\nconst stream = resolvePath('stream-browserify');\nconst _stream_duplex = resolvePath('readable-stream/lib/_stream_duplex.js');\nconst _stream_passthrough = resolvePath(\n\t'readable-stream/lib/_stream_passthrough.js'\n);\nconst _stream_readable = resolvePath('readable-stream/lib/_stream_readable.js');\nconst _stream_transform = resolvePath(\n\t'readable-stream/lib/_stream_transform.js'\n);\nconst _stream_writable = resolvePath('readable-stream/lib/_stream_writable.js');\nconst string_decoder = resolvePath('string_decoder/');\nconst sys = resolvePath('util/util.js');\nconst timers = resolvePath('timers-browserify');\nconst timersPromises = resolvePath('isomorphic-timers-promises');\nconst tls = resolvePath('./mock/empty.js');\nconst tty = resolvePath('tty-browserify');\nconst url = resolvePath('./proxy/url.js');\nconst util = resolvePath('util/util.js');\nconst vm = resolvePath('vm-browserify');\nconst zlib = resolvePath('browserify-zlib');\n\nconst packages = {\n\tassert,\n\tbuffer,\n\tchild_process,\n\tcluster,\n\tconsole: _console,\n\tconstants,\n\tcrypto,\n\tdgram,\n\tdns,\n\tdomain,\n\tevents,\n\tfs,\n\thttp,\n\thttps,\n\thttp2,\n\tmodule: _module,\n\tnet,\n\tos,\n\tpath,\n\tpunycode,\n\tprocess: _process,\n\tquerystring,\n\treadline,\n\trepl,\n\tstream,\n\t_stream_duplex,\n\t_stream_passthrough,\n\t_stream_readable,\n\t_stream_transform,\n\t_stream_writable,\n\tstring_decoder,\n\tsys,\n\t'timers/promises': timersPromises,\n\ttimers,\n\ttls,\n\ttty,\n\turl,\n\tutil,\n\tvm,\n\tzlib\n};\n\n/** @typedef {typeof packages} Packages */\n/** @typedef {keyof Packages} PackageNames */\n/** @typedef {{ [Property in PackageNames as `node:${Property}`]: Packages[Property] }} NodeProtocolPackages */\n\nconst packagesWithNodeProtocol = /** @type NodeProtocolPackages */ ({});\nfor (const [packageName, packagePath] of Object.entries(packages)) {\n\tpackagesWithNodeProtocol[\n\t\t`node:${/** @type PackageNames */ (packageName)}`\n\t] = /** @type PackageNames */ packagePath;\n}\n\nexport default {\n\t...packages,\n\t...packagesWithNodeProtocol\n};\n"], "names": ["<PERSON><PERSON><PERSON>", "path", "<PERSON><PERSON><PERSON>", "require", "resolve", "_globalThis$require", "_globalThis", "createRequire", "import", "includes", "_pkgDir$sync", "directory", "pkgDir", "sync", "assert", "buffer", "child_process", "cluster", "_console", "constants", "crypto", "dgram", "dns", "domain", "events", "fs", "http", "https", "http2", "_module", "net", "os", "punycode", "_process", "replace", "querystring", "readline", "repl", "stream", "_stream_duplex", "_stream_passthrough", "_stream_readable", "_stream_transform", "_stream_writable", "string_decoder", "sys", "timers", "timersPromises", "tls", "tty", "url", "util", "vm", "zlib", "packages", "console", "module", "process", "packagesWithNodeProtocol", "packageName", "packagePath", "Object", "entries"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAGA;AACA;AACA;AACA,MAAMA,WAAW,GAAIC,IAAI,IAAK;AAC7B,EAAA,IAAIC,YAAY,CAAA;EAChB,IAAI;AACHA,IAAAA,YAAY,GAAGC,OAAO,CAACC,OAAO,CAACH,IAAI,CAAC,CAAA;AACrC,GAAC,CAAC,MAAM;AAAA,IAAA,IAAAI,mBAAA,CAAA;IACPH,YAAY,GAAG,EAAAG,mBAAA,GACdC,WAAA,CAAWH,OAAO,KAAAE,IAAAA,GAAAA,mBAAA,GAAIE,iCAAa,CAACC,gQAAe,CAAC,EACnDJ,OAAO,CAACH,IAAI,CAAC,CAAA;AAChB,GAAA;AACA,EAAA,IAAI,CAACA,IAAI,CAACQ,QAAQ,CAAC,IAAI,CAAC,EAAE;AAAA,IAAA,IAAAC,YAAA,CAAA;AACzB,IAAA,MAAMC,SAAS,GAAA,CAAAD,YAAA,GAAGE,0BAAM,CAACC,IAAI,CAACX,YAAY,CAAC,KAAAQ,IAAAA,GAAAA,YAAA,GAAI,EAAE,CAAA;AACjD,IAAA,OAAOC,SAAS,CAAA;AACjB,GAAA;AACA,EAAA,OAAOT,YAAY,CAAA;AACpB,CAAC,CAAA;AAED,MAAMY,MAAM,GAAGd,WAAW,CAAC,SAAS,CAAC,CAAA;AACrC,MAAMe,MAAM,GAAGf,WAAW,CAAC,SAAS,CAAC,CAAA;AACrC,MAAMgB,aAAa,GAAGhB,WAAW,CAAC,iBAAiB,CAAC,CAAA;AACpD,MAAMiB,OAAO,GAAGjB,WAAW,CAAC,iBAAiB,CAAC,CAAA;AAC9C,MAAMkB,QAAQ,GAAGlB,WAAW,CAAC,oBAAoB,CAAC,CAAA;AAClD,MAAMmB,SAAS,GAAGnB,WAAW,CAAC,sBAAsB,CAAC,CAAA;AACrD,MAAMoB,MAAM,GAAGpB,WAAW,CAAC,mBAAmB,CAAC,CAAA;AAC/C,MAAMqB,KAAK,GAAGrB,WAAW,CAAC,iBAAiB,CAAC,CAAA;AAC5C,MAAMsB,GAAG,GAAGtB,WAAW,CAAC,iBAAiB,CAAC,CAAA;AAC1C,MAAMuB,MAAM,GAAGvB,WAAW,CAAC,gBAAgB,CAAC,CAAA;AAC5C,MAAMwB,MAAM,GAAGxB,WAAW,CAAC,SAAS,CAAC,CAAA;AACrC,MAAMyB,EAAE,GAAGzB,WAAW,CAAC,iBAAiB,CAAC,CAAA;AACzC,MAAM0B,IAAI,GAAG1B,WAAW,CAAC,aAAa,CAAC,CAAA;AACvC,MAAM2B,KAAK,GAAG3B,WAAW,CAAC,kBAAkB,CAAC,CAAA;AAC7C,MAAM4B,KAAK,GAAG5B,WAAW,CAAC,iBAAiB,CAAC,CAAA;AAC5C,MAAM6B,OAAO,GAAG7B,WAAW,CAAC,iBAAiB,CAAC,CAAA;AAC9C,MAAM8B,GAAG,GAAG9B,WAAW,CAAC,iBAAiB,CAAC,CAAA;AAC1C,MAAM+B,EAAE,GAAG/B,WAAW,CAAC,0BAA0B,CAAC,CAAA;AAClD,MAAMC,IAAI,GAAGD,WAAW,CAAC,iBAAiB,CAAC,CAAA;AAC3C,MAAMgC,QAAQ,GAAGhC,WAAW,CAAC,WAAW,CAAC,CAAA;AACzC,MAAMiC,QAAQ,GAAGjC,WAAW,CAAC,iBAAiB,CAAC,CAACkC,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,CAAA;AAClE,MAAMC,WAAW,GAAGnC,WAAW,CAAC,wBAAwB,CAAC,CAAA;AACzD,MAAMoC,QAAQ,GAAGpC,WAAW,CAAC,iBAAiB,CAAC,CAAA;AAC/C,MAAMqC,IAAI,GAAGrC,WAAW,CAAC,iBAAiB,CAAC,CAAA;AAC3C,MAAMsC,MAAM,GAAGtC,WAAW,CAAC,mBAAmB,CAAC,CAAA;AAC/C,MAAMuC,cAAc,GAAGvC,WAAW,CAAC,uCAAuC,CAAC,CAAA;AAC3E,MAAMwC,mBAAmB,GAAGxC,WAAW,CACtC,4CACD,CAAC,CAAA;AACD,MAAMyC,gBAAgB,GAAGzC,WAAW,CAAC,yCAAyC,CAAC,CAAA;AAC/E,MAAM0C,iBAAiB,GAAG1C,WAAW,CACpC,0CACD,CAAC,CAAA;AACD,MAAM2C,gBAAgB,GAAG3C,WAAW,CAAC,yCAAyC,CAAC,CAAA;AAC/E,MAAM4C,cAAc,GAAG5C,WAAW,CAAC,iBAAiB,CAAC,CAAA;AACrD,MAAM6C,GAAG,GAAG7C,WAAW,CAAC,cAAc,CAAC,CAAA;AACvC,MAAM8C,MAAM,GAAG9C,WAAW,CAAC,mBAAmB,CAAC,CAAA;AAC/C,MAAM+C,cAAc,GAAG/C,WAAW,CAAC,4BAA4B,CAAC,CAAA;AAChE,MAAMgD,GAAG,GAAGhD,WAAW,CAAC,iBAAiB,CAAC,CAAA;AAC1C,MAAMiD,GAAG,GAAGjD,WAAW,CAAC,gBAAgB,CAAC,CAAA;AACzC,MAAMkD,GAAG,GAAGlD,WAAW,CAAC,gBAAgB,CAAC,CAAA;AACzC,MAAMmD,IAAI,GAAGnD,WAAW,CAAC,cAAc,CAAC,CAAA;AACxC,MAAMoD,EAAE,GAAGpD,WAAW,CAAC,eAAe,CAAC,CAAA;AACvC,MAAMqD,IAAI,GAAGrD,WAAW,CAAC,iBAAiB,CAAC,CAAA;AAE3C,MAAMsD,QAAQ,GAAG;EAChBxC,MAAM;EACNC,MAAM;EACNC,aAAa;EACbC,OAAO;AACPsC,EAAAA,OAAO,EAAErC,QAAQ;EACjBC,SAAS;EACTC,MAAM;EACNC,KAAK;EACLC,GAAG;EACHC,MAAM;EACNC,MAAM;EACNC,EAAE;EACFC,IAAI;EACJC,KAAK;EACLC,KAAK;AACL4B,EAAAA,MAAM,EAAE3B,OAAO;EACfC,GAAG;EACHC,EAAE;EACF9B,IAAI;EACJ+B,QAAQ;AACRyB,EAAAA,OAAO,EAAExB,QAAQ;EACjBE,WAAW;EACXC,QAAQ;EACRC,IAAI;EACJC,MAAM;EACNC,cAAc;EACdC,mBAAmB;EACnBC,gBAAgB;EAChBC,iBAAiB;EACjBC,gBAAgB;EAChBC,cAAc;EACdC,GAAG;AACH,EAAA,iBAAiB,EAAEE,cAAc;EACjCD,MAAM;EACNE,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,IAAI;EACJC,EAAE;AACFC,EAAAA,IAAAA;AACD,CAAC,CAAA;;AAED;AACA;AACA;;AAEA,MAAMK,wBAAwB,oCAAsC,EAAG,CAAA;AACvE,KAAK,MAAM,CAACC,WAAW,EAAEC,WAAW,CAAC,IAAIC,MAAM,CAACC,OAAO,CAACR,QAAQ,CAAC,EAAE;EAClEI,wBAAwB,CACvB,kCAAmCC,WAAW,GAAG,CACjD,4BAA6BC,WAAW,CAAA;AAC1C,CAAA;AAEA,YAAe;AACd,EAAA,GAAGN,QAAQ;EACX,GAAGI,wBAAAA;AACJ,CAAC;;;;"}