import ccxt from 'ccxt'
import type { Exchange, Symbol, Ticker, ApiResponse } from '@/types'
import { currencyConverter } from '@/utils/currencyConverter'

class RealCCXTService {
  private exchanges: Map<string, ccxt.Exchange> = new Map()
  private exchangeLogos: Map<string, string> = new Map()

  constructor() {
    this.initializeExchangeLogos()
  }

  // 初始化交易所Logo映射
  private initializeExchangeLogos() {
    const logos = {
      binance: 'https://user-images.githubusercontent.com/1294454/29604020-d5483cdc-87ee-11e7-94c7-d1a8d9169293.jpg',
      okx: 'https://user-images.githubusercontent.com/1294454/152485636-38b19e4a-bece-4dec-979a-5982859ffc04.jpg',
      bybit: 'https://user-images.githubusercontent.com/1294454/165102362-f47a0cea-d0c6-4c6f-b4c8-dcf6a6c7f55e.jpg',
      coinbase: 'https://user-images.githubusercontent.com/1294454/40811661-b6eceae2-653a-11e8-829e-10b03607bc2c.jpg',
      kraken: 'https://user-images.githubusercontent.com/1294454/27766599-22709304-5ede-11e7-9de1-9f33732e5509.jpg',
      huobi: 'https://user-images.githubusercontent.com/1294454/76137448-22748a80-604e-11ea-8069-6e389271911d.jpg',
      kucoin: 'https://user-images.githubusercontent.com/1294454/57369448-3cc3aa80-7196-11e9-883e-5ebeb35e4f57.jpg',
      gateio: 'https://user-images.githubusercontent.com/1294454/31784029-0313c702-b509-11e7-9ccc-bc0da6a0e435.jpg',
      bitfinex: 'https://user-images.githubusercontent.com/1294454/27766244-e328a50c-5ed2-11e7-947b-041416579bb3.jpg',
      bitmex: 'https://user-images.githubusercontent.com/1294454/27766319-f653c6e6-5ed4-11e7-933d-f0bc3699ae8f.jpg',
      bitget: 'https://user-images.githubusercontent.com/1294454/195989417-4253ddb0-afbe-4a1c-9dea-9dbcd121fa5d.jpg',
      mexc: 'https://user-images.githubusercontent.com/1294454/137283979-8b2a818d-8633-461b-bfca-de89e8c446b2.jpg',
      phemex: 'https://user-images.githubusercontent.com/1294454/85225056-221eb600-b3f4-11ea-930d-564d2690e3f6.jpg',
      bitmart: 'https://user-images.githubusercontent.com/1294454/129991357-8f47464b-d0f4-41d6-8a82-34122f0d1398.jpg',
      cryptocom: 'https://user-images.githubusercontent.com/1294454/147792121-38ed5e36-c229-48d6-b49a-48d05fc19ed4.jpg',
      gemini: 'https://user-images.githubusercontent.com/1294454/27816857-ce7be644-6096-11e7-82d6-3c257263229c.jpg',
      bitstamp: 'https://user-images.githubusercontent.com/1294454/27786377-8c8ab57e-5fe9-11e7-8ea4-2b05b6bcceec.jpg'
    }
    
    Object.entries(logos).forEach(([id, logo]) => {
      this.exchangeLogos.set(id, logo)
    })
  }

  // 获取所有支持的交易所列表
  async getAllExchanges(): Promise<ApiResponse<Exchange[]>> {
    try {
      const exchangeIds = ccxt.exchanges
      const exchanges: Exchange[] = []

      // 只处理主要的交易所，避免加载过多
      const mainExchanges = [
        'binance', 'okx', 'bybit', 'coinbase', 'kraken', 'huobi', 'kucoin', 
        'gateio', 'bitfinex', 'bitmex', 'bitget', 'mexc', 'phemex', 
        'bitmart', 'cryptocom', 'gemini', 'bitstamp'
      ]

      for (const id of mainExchanges) {
        if (exchangeIds.includes(id)) {
          try {
            const ExchangeClass = ccxt[id as keyof typeof ccxt] as any
            if (typeof ExchangeClass === 'function') {
              // 创建交易所实例但不立即连接
              const exchange = new ExchangeClass({
                sandbox: false,
                enableRateLimit: true,
                timeout: 10000,
              })

              const exchangeInfo: Exchange = {
                id: exchange.id,
                name: exchange.name,
                countries: exchange.countries || [],
                urls: {
                  logo: this.exchangeLogos.get(id) || exchange.urls?.logo,
                  www: exchange.urls?.www,
                  api: exchange.urls?.api
                },
                has: exchange.has || {},
                rateLimit: exchange.rateLimit || 1000,
                certified: exchange.certified || false,
                pro: exchange.pro || false,
                alias: exchange.alias || false,
                spot: exchange.has?.spot || false,
                margin: exchange.has?.margin || false,
                future: exchange.has?.future || false,
                option: exchange.has?.option || false,
                status: 'ok',
                // 模拟24h交易量数据
                volume24h: Math.random() * 10000000000 + 1000000000
              }

              exchanges.push(exchangeInfo)
              
              // 缓存交易所实例
              this.exchanges.set(id, exchange)
            }
          } catch (error) {
            console.warn(`Failed to initialize exchange ${id}:`, error)
          }
        }
      }

      return {
        success: true,
        data: exchanges.sort((a, b) => a.name.localeCompare(b.name))
      }
    } catch (error) {
      return {
        success: false,
        data: [],
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    }
  }

  // 获取指定交易所的所有交易对
  async getExchangeSymbols(exchangeId: string): Promise<ApiResponse<Symbol[]>> {
    try {
      let exchange = this.exchanges.get(exchangeId)
      
      if (!exchange) {
        const ExchangeClass = ccxt[exchangeId as keyof typeof ccxt] as any
        if (typeof ExchangeClass !== 'function') {
          throw new Error(`Exchange ${exchangeId} not found`)
        }
        
        exchange = new ExchangeClass({
          sandbox: false,
          enableRateLimit: true,
          timeout: 10000,
        })
        this.exchanges.set(exchangeId, exchange)
      }

      await exchange.loadMarkets()
      const markets = exchange.markets
      const symbols: Symbol[] = []

      // 限制返回的交易对数量，避免过多数据
      const marketEntries = Object.entries(markets).slice(0, 100)

      for (const [marketId, market] of marketEntries) {
        try {
          const marketData = market as any
          
          // 模拟ticker数据
          const price = Math.random() * 1000 + 1
          const change = (Math.random() - 0.5) * 20
          const baseVolume = Math.random() * 100000 + 1000
          const quoteVolume = baseVolume * price

          // 计算USDT等值交易量
          const usdtVolume = currencyConverter.calculateUSDTVolume(
            baseVolume,
            quoteVolume,
            marketData.base,
            marketData.quote
          )

          const symbol: Symbol = {
            id: marketData.id,
            symbol: marketData.symbol,
            base: marketData.base,
            quote: marketData.quote,
            baseId: marketData.baseId,
            quoteId: marketData.quoteId,
            active: marketData.active,
            type: marketData.type || 'spot',
            spot: marketData.spot || false,
            margin: marketData.margin || false,
            future: marketData.future || false,
            option: marketData.option || false,
            contract: marketData.contract || false,
            linear: marketData.linear,
            inverse: marketData.inverse,
            contractSize: marketData.contractSize,
            expiry: marketData.expiry,
            expiryDatetime: marketData.expiryDatetime,
            strike: marketData.strike,
            optionType: marketData.optionType,
            precision: marketData.precision || { amount: 8, price: 8 },
            limits: marketData.limits || {
              amount: { min: 0, max: undefined },
              price: { min: 0, max: undefined },
              cost: { min: 0, max: undefined }
            },
            info: marketData.info,
            exchange: exchangeId,
            exchangeName: exchange.name,
            exchangeLogo: this.exchangeLogos.get(exchangeId) || exchange.urls?.logo,
            usdtVolume,
            ticker: {
              symbol: marketData.symbol,
              timestamp: Date.now(),
              datetime: new Date().toISOString(),
              high: price * 1.1,
              low: price * 0.9,
              bid: price * 0.999,
              bidVolume: Math.random() * 10,
              ask: price * 1.001,
              askVolume: Math.random() * 10,
              vwap: price,
              open: price - change,
              close: price,
              last: price,
              previousClose: price - change,
              change,
              percentage: (change / (price - change)) * 100,
              average: price,
              baseVolume,
              quoteVolume,
              info: {}
            }
          }
          symbols.push(symbol)
        } catch (error) {
          console.warn(`Failed to process market ${marketId}:`, error)
        }
      }

      return {
        success: true,
        data: symbols
      }
    } catch (error) {
      return {
        success: false,
        data: [],
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    }
  }

  // 获取所有交易所的交易对
  async getAllSymbols(): Promise<ApiResponse<Symbol[]>> {
    try {
      const exchangesResponse = await this.getAllExchanges()
      if (!exchangesResponse.success) {
        return exchangesResponse as ApiResponse<Symbol[]>
      }

      const allSymbols: Symbol[] = []
      const popularExchanges = ['binance', 'okx', 'bybit', 'coinbase', 'kraken']

      for (const exchangeId of popularExchanges) {
        try {
          const symbolsResponse = await this.getExchangeSymbols(exchangeId)
          if (symbolsResponse.success) {
            allSymbols.push(...symbolsResponse.data)
          }
        } catch (error) {
          console.warn(`Failed to get symbols for ${exchangeId}:`, error)
        }
      }

      return {
        success: true,
        data: allSymbols
      }
    } catch (error) {
      return {
        success: false,
        data: [],
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    }
  }

  // 获取交易对的24小时行情数据
  async getTicker(exchangeId: string, symbol: string): Promise<ApiResponse<Ticker | null>> {
    try {
      let exchange = this.exchanges.get(exchangeId)
      
      if (!exchange) {
        const ExchangeClass = ccxt[exchangeId as keyof typeof ccxt] as any
        exchange = new ExchangeClass({
          sandbox: false,
          enableRateLimit: true,
          timeout: 10000,
        })
        this.exchanges.set(exchangeId, exchange)
      }

      if (!exchange.has.fetchTicker) {
        return {
          success: false,
          data: null,
          error: `Exchange ${exchangeId} does not support ticker data`
        }
      }

      const ticker = await exchange.fetchTicker(symbol)
      
      return {
        success: true,
        data: ticker as Ticker
      }
    } catch (error) {
      return {
        success: false,
        data: null,
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    }
  }

  // 批量获取多个交易对的行情数据
  async getMultipleTickers(requests: Array<{exchangeId: string, symbol: string}>): Promise<ApiResponse<Ticker[]>> {
    try {
      const tickers: Ticker[] = []
      
      for (const request of requests) {
        try {
          const tickerResponse = await this.getTicker(request.exchangeId, request.symbol)
          if (tickerResponse.success && tickerResponse.data) {
            tickers.push(tickerResponse.data)
          }
        } catch (error) {
          console.warn(`Failed to get ticker for ${request.exchangeId}:${request.symbol}:`, error)
        }
      }

      return {
        success: true,
        data: tickers
      }
    } catch (error) {
      return {
        success: false,
        data: [],
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    }
  }
}

export const ccxtService = new RealCCXTService()
export default ccxtService
