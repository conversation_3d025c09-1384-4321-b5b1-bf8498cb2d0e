<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CCXT 简单测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 50px auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 { color: #333; text-align: center; }
        .status {
            padding: 15px;
            margin: 20px 0;
            border-radius: 5px;
            font-weight: bold;
        }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        .warning { background: #fff3cd; color: #856404; }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            margin: 5px;
            border-radius: 5px;
            cursor: pointer;
        }
        button:hover { background: #0056b3; }
        button:disabled { background: #ccc; cursor: not-allowed; }
        #output {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 15px;
            margin: 20px 0;
            border-radius: 5px;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
        }
        .test-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 10px;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 CCXT 简单测试</h1>
        
        <div id="status" class="status warning">
            正在检测 CCXT 库...
        </div>

        <div class="test-grid">
            <button onclick="quickTest()" id="quickBtn">快速测试</button>
            <button onclick="testExchangeList()" id="listBtn">交易所列表</button>
            <button onclick="testBinanceMarkets()" id="binanceBtn">Binance市场</button>
            <button onclick="testTicker()" id="tickerBtn">获取价格</button>
            <button onclick="clearLog()" style="background: #6c757d;">清空日志</button>
        </div>

        <div id="output">等待测试...</div>
    </div>

    <script src="https://unpkg.com/ccxt@latest/dist/ccxt.browser.js"></script>
    
    <script>
        const output = document.getElementById('output');
        const status = document.getElementById('status');
        let ccxtReady = false;

        function log(msg) {
            const time = new Date().toLocaleTimeString();
            output.textContent += `[${time}] ${msg}\n`;
            output.scrollTop = output.scrollHeight;
            console.log(msg);
        }

        function clearLog() {
            output.textContent = '';
        }

        // 检查CCXT状态
        function checkCCXT() {
            if (typeof ccxt !== 'undefined') {
                ccxtReady = true;
                status.className = 'status success';
                status.textContent = '✅ CCXT 加载成功！';
                document.querySelectorAll('button').forEach(btn => btn.disabled = false);
                log('CCXT 库加载成功');
                log(`版本: ${ccxt.version || '未知'}`);
                log(`支持交易所: ${ccxt.exchanges.length} 个`);
            } else {
                status.className = 'status error';
                status.textContent = '❌ CCXT 加载失败';
                document.querySelectorAll('button').forEach(btn => {
                    if (btn.textContent !== '清空日志') btn.disabled = true;
                });
                log('CCXT 库加载失败');
            }
        }

        // 快速测试
        async function quickTest() {
            if (!ccxtReady) return;
            
            log('=== 快速测试开始 ===');
            try {
                // 测试创建交易所
                const binance = new ccxt.binance();
                log(`✅ 创建 ${binance.name} 成功`);
                
                // 测试基本属性
                log(`ID: ${binance.id}`);
                log(`国家: ${binance.countries.join(', ')}`);
                log(`现货支持: ${binance.has.spot ? '是' : '否'}`);
                log(`期货支持: ${binance.has.future ? '是' : '否'}`);
                
                log('✅ 快速测试完成');
            } catch (error) {
                log(`❌ 快速测试失败: ${error.message}`);
            }
        }

        // 测试交易所列表
        async function testExchangeList() {
            if (!ccxtReady) return;
            
            log('=== 交易所列表测试 ===');
            try {
                const exchanges = ccxt.exchanges.slice(0, 10);
                log(`前10个交易所: ${exchanges.join(', ')}`);
                
                for (const id of exchanges.slice(0, 3)) {
                    try {
                        const ExchangeClass = ccxt[id];
                        if (typeof ExchangeClass === 'function') {
                            const exchange = new ExchangeClass();
                            log(`✅ ${exchange.name} (${id})`);
                        }
                    } catch (error) {
                        log(`❌ ${id}: ${error.message}`);
                    }
                }
            } catch (error) {
                log(`❌ 交易所列表测试失败: ${error.message}`);
            }
        }

        // 测试Binance市场
        async function testBinanceMarkets() {
            if (!ccxtReady) return;
            
            log('=== Binance 市场测试 ===');
            try {
                const binance = new ccxt.binance({ enableRateLimit: true });
                log('正在加载市场数据...');
                
                const markets = await binance.loadMarkets();
                const symbols = Object.keys(markets);
                
                log(`✅ 加载成功，共 ${symbols.length} 个交易对`);
                log(`前5个交易对: ${symbols.slice(0, 5).join(', ')}`);
                
                // 显示一些热门交易对
                const popular = ['BTC/USDT', 'ETH/USDT', 'BNB/USDT'];
                const available = popular.filter(s => symbols.includes(s));
                log(`可用的热门交易对: ${available.join(', ')}`);
                
            } catch (error) {
                log(`❌ Binance 市场测试失败: ${error.message}`);
            }
        }

        // 测试获取价格
        async function testTicker() {
            if (!ccxtReady) return;
            
            log('=== 价格获取测试 ===');
            try {
                const binance = new ccxt.binance({ enableRateLimit: true });
                await binance.loadMarkets();
                
                const symbol = 'BTC/USDT';
                log(`正在获取 ${symbol} 价格...`);
                
                const ticker = await binance.fetchTicker(symbol);
                
                log(`✅ ${symbol} 价格信息:`);
                log(`  最新价格: $${ticker.last}`);
                log(`  24h最高: $${ticker.high}`);
                log(`  24h最低: $${ticker.low}`);
                log(`  24h涨跌: ${ticker.change ? '$' + ticker.change.toFixed(2) : 'N/A'}`);
                log(`  24h涨跌幅: ${ticker.percentage ? ticker.percentage.toFixed(2) + '%' : 'N/A'}`);
                log(`  24h成交量: ${ticker.baseVolume ? ticker.baseVolume.toFixed(2) + ' BTC' : 'N/A'}`);
                
            } catch (error) {
                log(`❌ 价格获取测试失败: ${error.message}`);
            }
        }

        // 页面加载后检查CCXT
        window.addEventListener('load', () => {
            // 禁用所有按钮
            document.querySelectorAll('button').forEach(btn => {
                if (btn.textContent !== '清空日志') btn.disabled = true;
            });
            
            // 延迟检查CCXT
            setTimeout(checkCCXT, 1000);
        });

        // 错误处理
        window.addEventListener('error', (event) => {
            log(`❌ 页面错误: ${event.error?.message || event.message}`);
        });

        // 未处理的Promise错误
        window.addEventListener('unhandledrejection', (event) => {
            log(`❌ Promise错误: ${event.reason?.message || event.reason}`);
        });
    </script>
</body>
</html>
