import type { Exchange, Symbol, Ticker, ApiResponse } from '@/types'
import { currencyConverter } from '@/utils/currencyConverter'

// 导入模拟服务作为fallback
import mockCcxtService from './mockCcxtService'

class HybridCCXTService {
  private ccxtAvailable = false
  private ccxt: any = null

  constructor() {
    this.initializeCCXT()
  }

  // 尝试初始化CCXT
  private async initializeCCXT() {
    try {
      // 检查window.ccxt是否可用
      if (typeof window !== 'undefined' && (window as any).ccxt) {
        this.ccxt = (window as any).ccxt
        this.ccxtAvailable = true
        console.log('Using real CCXT library')
        return
      }

      // 尝试动态导入CCXT
      const ccxtModule = await import('ccxt')
      this.ccxt = ccxtModule.default || ccxtModule
      this.ccxtAvailable = true
      console.log('CCXT loaded successfully')
    } catch (error) {
      console.warn('CCXT not available, using mock data:', error)
      this.ccxtAvailable = false
    }
  }

  // 获取所有支持的交易所列表
  async getAllExchanges(): Promise<ApiResponse<Exchange[]>> {
    if (!this.ccxtAvailable) {
      console.log('Using mock exchange data')
      return mockCcxtService.getAllExchanges()
    }

    try {
      const exchangeIds = this.ccxt.exchanges || []
      const exchanges: Exchange[] = []

      // 主要交易所列表
      const mainExchanges = [
        'binance', 'okx', 'bybit', 'coinbase', 'kraken', 'huobi', 'kucoin', 
        'gateio', 'bitfinex', 'bitmex', 'bitget', 'mexc', 'phemex', 
        'bitmart', 'cryptocom', 'gemini', 'bitstamp'
      ]

      const logoMap = {
        binance: 'https://user-images.githubusercontent.com/1294454/29604020-d5483cdc-87ee-11e7-94c7-d1a8d9169293.jpg',
        okx: 'https://user-images.githubusercontent.com/1294454/152485636-38b19e4a-bece-4dec-979a-5982859ffc04.jpg',
        bybit: 'https://user-images.githubusercontent.com/1294454/165102362-f47a0cea-d0c6-4c6f-b4c8-dcf6a6c7f55e.jpg',
        coinbase: 'https://user-images.githubusercontent.com/1294454/40811661-b6eceae2-653a-11e8-829e-10b03607bc2c.jpg',
        kraken: 'https://user-images.githubusercontent.com/1294454/27766599-22709304-5ede-11e7-9de1-9f33732e5509.jpg',
        huobi: 'https://user-images.githubusercontent.com/1294454/76137448-22748a80-604e-11ea-8069-6e389271911d.jpg',
        kucoin: 'https://user-images.githubusercontent.com/1294454/57369448-3cc3aa80-7196-11e9-883e-5ebeb35e4f57.jpg',
        gateio: 'https://user-images.githubusercontent.com/1294454/31784029-0313c702-b509-11e7-9ccc-bc0da6a0e435.jpg',
        bitfinex: 'https://user-images.githubusercontent.com/1294454/27766244-e328a50c-5ed2-11e7-947b-041416579bb3.jpg',
        bitmex: 'https://user-images.githubusercontent.com/1294454/27766319-f653c6e6-5ed4-11e7-933d-f0bc3699ae8f.jpg',
        bitget: 'https://user-images.githubusercontent.com/1294454/195989417-4253ddb0-afbe-4a1c-9dea-9dbcd121fa5d.jpg',
        mexc: 'https://user-images.githubusercontent.com/1294454/137283979-8b2a818d-8633-461b-bfca-de89e8c446b2.jpg',
        phemex: 'https://user-images.githubusercontent.com/1294454/85225056-221eb600-b3f4-11ea-930d-564d2690e3f6.jpg',
        bitmart: 'https://user-images.githubusercontent.com/1294454/129991357-8f47464b-d0f4-41d6-8a82-34122f0d1398.jpg',
        cryptocom: 'https://user-images.githubusercontent.com/1294454/147792121-38ed5e36-c229-48d6-b49a-48d05fc19ed4.jpg',
        gemini: 'https://user-images.githubusercontent.com/1294454/27816857-ce7be644-6096-11e7-82d6-3c257263229c.jpg',
        bitstamp: 'https://user-images.githubusercontent.com/1294454/27786377-8c8ab57e-5fe9-11e7-8ea4-2b05b6bcceec.jpg'
      }

      for (const id of mainExchanges) {
        if (exchangeIds.includes(id)) {
          try {
            const ExchangeClass = this.ccxt[id]
            if (typeof ExchangeClass === 'function') {
              // 创建交易所实例但不立即连接
              const exchange = new ExchangeClass({
                sandbox: false,
                enableRateLimit: true,
                timeout: 10000,
              })

              const exchangeInfo: Exchange = {
                id: exchange.id,
                name: exchange.name,
                countries: exchange.countries || [],
                urls: {
                  logo: logoMap[id as keyof typeof logoMap] || exchange.urls?.logo,
                  www: exchange.urls?.www,
                  api: exchange.urls?.api
                },
                has: exchange.has || {},
                rateLimit: exchange.rateLimit || 1000,
                certified: exchange.certified || false,
                pro: exchange.pro || false,
                alias: exchange.alias || false,
                spot: exchange.has?.spot || false,
                margin: exchange.has?.margin || false,
                future: exchange.has?.future || false,
                option: exchange.has?.option || false,
                status: 'ok',
                // 模拟24h交易量数据
                volume24h: Math.random() * 10000000000 + 1000000000
              }

              exchanges.push(exchangeInfo)
            }
          } catch (error) {
            console.warn(`Failed to initialize exchange ${id}:`, error)
          }
        }
      }

      return {
        success: true,
        data: exchanges.sort((a, b) => a.name.localeCompare(b.name))
      }
    } catch (error) {
      console.warn('Real CCXT failed, falling back to mock data:', error)
      return mockCcxtService.getAllExchanges()
    }
  }

  // 获取指定交易所的所有交易对
  async getExchangeSymbols(exchangeId: string): Promise<ApiResponse<Symbol[]>> {
    if (!this.ccxtAvailable) {
      return mockCcxtService.getExchangeSymbols(exchangeId)
    }

    try {
      const ExchangeClass = this.ccxt[exchangeId]
      if (typeof ExchangeClass !== 'function') {
        throw new Error(`Exchange ${exchangeId} not found`)
      }

      const exchange = new ExchangeClass({
        sandbox: false,
        enableRateLimit: true,
        timeout: 15000,
      })

      // 尝试加载市场数据
      await exchange.loadMarkets()
      const markets = exchange.markets
      const symbols: Symbol[] = []

      // 限制返回的交易对数量，避免过多数据
      const marketEntries = Object.entries(markets).slice(0, 50)

      for (const [marketId, market] of marketEntries) {
        try {
          const marketData = market as any
          
          // 模拟ticker数据
          const price = Math.random() * 1000 + 1
          const change = (Math.random() - 0.5) * 20
          const baseVolume = Math.random() * 100000 + 1000
          const quoteVolume = baseVolume * price

          // 计算USDT等值交易量
          const usdtVolume = currencyConverter.calculateUSDTVolume(
            baseVolume,
            quoteVolume,
            marketData.base,
            marketData.quote
          )

          const symbol: Symbol = {
            id: marketData.id,
            symbol: marketData.symbol,
            base: marketData.base,
            quote: marketData.quote,
            baseId: marketData.baseId,
            quoteId: marketData.quoteId,
            active: marketData.active,
            type: marketData.type || 'spot',
            spot: marketData.spot || false,
            margin: marketData.margin || false,
            future: marketData.future || false,
            option: marketData.option || false,
            contract: marketData.contract || false,
            precision: marketData.precision || { amount: 8, price: 8 },
            limits: marketData.limits || {
              amount: { min: 0, max: undefined },
              price: { min: 0, max: undefined },
              cost: { min: 0, max: undefined }
            },
            info: marketData.info,
            exchange: exchangeId,
            exchangeName: exchange.name,
            exchangeLogo: exchange.urls?.logo,
            usdtVolume,
            ticker: {
              symbol: marketData.symbol,
              timestamp: Date.now(),
              datetime: new Date().toISOString(),
              high: price * 1.1,
              low: price * 0.9,
              bid: price * 0.999,
              bidVolume: Math.random() * 10,
              ask: price * 1.001,
              askVolume: Math.random() * 10,
              vwap: price,
              open: price - change,
              close: price,
              last: price,
              previousClose: price - change,
              change,
              percentage: (change / (price - change)) * 100,
              average: price,
              baseVolume,
              quoteVolume,
              info: {}
            }
          }
          symbols.push(symbol)
        } catch (error) {
          console.warn(`Failed to process market ${marketId}:`, error)
        }
      }

      return {
        success: true,
        data: symbols
      }
    } catch (error) {
      console.warn(`Real CCXT failed for ${exchangeId}, falling back to mock data:`, error)
      return mockCcxtService.getExchangeSymbols(exchangeId)
    }
  }

  // 获取所有交易所的交易对
  async getAllSymbols(): Promise<ApiResponse<Symbol[]>> {
    if (!this.ccxtAvailable) {
      return mockCcxtService.getAllSymbols()
    }

    try {
      const allSymbols: Symbol[] = []
      const popularExchanges = ['binance', 'okx', 'bybit']

      for (const exchangeId of popularExchanges) {
        try {
          const symbolsResponse = await this.getExchangeSymbols(exchangeId)
          if (symbolsResponse.success) {
            allSymbols.push(...symbolsResponse.data)
          }
        } catch (error) {
          console.warn(`Failed to get symbols for ${exchangeId}:`, error)
        }
      }

      return {
        success: true,
        data: allSymbols
      }
    } catch (error) {
      console.warn('Real CCXT failed, falling back to mock data:', error)
      return mockCcxtService.getAllSymbols()
    }
  }

  // 其他方法直接委托给模拟服务
  async getTicker(exchangeId: string, symbol: string): Promise<ApiResponse<Ticker | null>> {
    return mockCcxtService.getTicker(exchangeId, symbol)
  }

  async getMultipleTickers(requests: Array<{exchangeId: string, symbol: string}>): Promise<ApiResponse<Ticker[]>> {
    return mockCcxtService.getMultipleTickers(requests)
  }
}

export const ccxtService = new HybridCCXTService()
export default ccxtService
