'use strict';

Object.defineProperty(exports, '__esModule', { value: true });

/* globals unknown */

/**
 * @param {unknown[]} arguments_
 */
var api = function api() {
  var _ref;
  if (arguments.length === 0) {
    return;
  }
  var callback = (_ref = arguments.length - 1, _ref < 0 || arguments.length <= _ref ? undefined : arguments[_ref]);
  if (typeof callback === 'function') {
    callback(null, '0.0.0.0');
  }
};

exports.lookup = api;
exports.resolve = api;
exports.resolve4 = api;
exports.resolve6 = api;
exports.resolveCname = api;
exports.resolveMx = api;
exports.resolveNaptr = api;
exports.resolveNs = api;
exports.resolveSrv = api;
exports.resolveTxt = api;
exports.reverse = api;
//# sourceMappingURL=dns.js.map
