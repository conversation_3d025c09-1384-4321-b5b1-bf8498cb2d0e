declare const _default: {
    "node:assert": string;
    "node:buffer": string;
    "node:child_process": string;
    "node:cluster": string;
    "node:console": string;
    "node:constants": string;
    "node:crypto": string;
    "node:dgram": string;
    "node:dns": string;
    "node:domain": string;
    "node:events": string;
    "node:fs": string;
    "node:http": string;
    "node:https": string;
    "node:http2": string;
    "node:module": string;
    "node:net": string;
    "node:os": string;
    "node:path": string;
    "node:punycode": string;
    "node:process": string;
    "node:querystring": string;
    "node:readline": string;
    "node:repl": string;
    "node:stream": string;
    "node:_stream_duplex": string;
    "node:_stream_passthrough": string;
    "node:_stream_readable": string;
    "node:_stream_transform": string;
    "node:_stream_writable": string;
    "node:string_decoder": string;
    "node:sys": string;
    "node:timers/promises": string;
    "node:timers": string;
    "node:tls": string;
    "node:tty": string;
    "node:url": string;
    "node:util": string;
    "node:vm": string;
    "node:zlib": string;
    assert: string;
    buffer: string;
    child_process: string;
    cluster: string;
    console: string;
    constants: string;
    crypto: string;
    dgram: string;
    dns: string;
    domain: string;
    events: string;
    fs: string;
    http: string;
    https: string;
    http2: string;
    module: string;
    net: string;
    os: string;
    path: string;
    punycode: string;
    process: string;
    querystring: string;
    readline: string;
    repl: string;
    stream: string;
    _stream_duplex: string;
    _stream_passthrough: string;
    _stream_readable: string;
    _stream_transform: string;
    _stream_writable: string;
    string_decoder: string;
    sys: string;
    'timers/promises': string;
    timers: string;
    tls: string;
    tty: string;
    url: string;
    util: string;
    vm: string;
    zlib: string;
};
export default _default;
export type Packages = typeof packages;
export type PackageNames = keyof Packages;
export type NodeProtocolPackages = {
    "node:assert": string;
    "node:buffer": string;
    "node:child_process": string;
    "node:cluster": string;
    "node:console": string;
    "node:constants": string;
    "node:crypto": string;
    "node:dgram": string;
    "node:dns": string;
    "node:domain": string;
    "node:events": string;
    "node:fs": string;
    "node:http": string;
    "node:https": string;
    "node:http2": string;
    "node:module": string;
    "node:net": string;
    "node:os": string;
    "node:path": string;
    "node:punycode": string;
    "node:process": string;
    "node:querystring": string;
    "node:readline": string;
    "node:repl": string;
    "node:stream": string;
    "node:_stream_duplex": string;
    "node:_stream_passthrough": string;
    "node:_stream_readable": string;
    "node:_stream_transform": string;
    "node:_stream_writable": string;
    "node:string_decoder": string;
    "node:sys": string;
    "node:timers/promises": string;
    "node:timers": string;
    "node:tls": string;
    "node:tty": string;
    "node:url": string;
    "node:util": string;
    "node:vm": string;
    "node:zlib": string;
};
declare const packages: {
    assert: string;
    buffer: string;
    child_process: string;
    cluster: string;
    console: string;
    constants: string;
    crypto: string;
    dgram: string;
    dns: string;
    domain: string;
    events: string;
    fs: string;
    http: string;
    https: string;
    http2: string;
    module: string;
    net: string;
    os: string;
    path: string;
    punycode: string;
    process: string;
    querystring: string;
    readline: string;
    repl: string;
    stream: string;
    _stream_duplex: string;
    _stream_passthrough: string;
    _stream_readable: string;
    _stream_transform: string;
    _stream_writable: string;
    string_decoder: string;
    sys: string;
    'timers/promises': string;
    timers: string;
    tls: string;
    tty: string;
    url: string;
    util: string;
    vm: string;
    zlib: string;
};
//# sourceMappingURL=index.d.ts.map