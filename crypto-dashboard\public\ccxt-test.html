<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CCXT 测试页面</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 12px;
            padding: 30px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }

        h1 {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
            font-size: 2.5em;
        }

        .status {
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            font-weight: bold;
        }

        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .status.warning {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }

        .controls {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 30px;
        }

        button {
            padding: 12px 20px;
            border: none;
            border-radius: 6px;
            background: #007bff;
            color: white;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s ease;
        }

        button:hover {
            background: #0056b3;
            transform: translateY(-2px);
        }

        button:disabled {
            background: #6c757d;
            cursor: not-allowed;
            transform: none;
        }

        .output {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            max-height: 400px;
            overflow-y: auto;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            line-height: 1.5;
        }

        .exchange-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }

        .exchange-card {
            background: white;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 15px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            transition: transform 0.2s ease;
        }

        .exchange-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
        }

        .exchange-logo {
            width: 32px;
            height: 32px;
            border-radius: 4px;
            margin-right: 10px;
            vertical-align: middle;
        }

        .exchange-name {
            font-weight: bold;
            color: #333;
        }

        .exchange-id {
            color: #666;
            font-size: 12px;
        }

        .loading {
            text-align: center;
            padding: 20px;
            color: #666;
        }

        .spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #3498db;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 2s linear infinite;
            margin: 0 auto 10px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .info-box {
            background: #e7f3ff;
            border: 1px solid #b3d9ff;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 20px;
        }

        .info-box h3 {
            color: #0066cc;
            margin-bottom: 10px;
        }

        .info-box ul {
            margin-left: 20px;
        }

        .info-box li {
            margin-bottom: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 CCXT 测试页面</h1>
        
        <div id="status" class="status warning">
            正在检测 CCXT 库...
        </div>

        <div class="info-box">
            <h3>📋 测试说明</h3>
            <ul>
                <li>本页面用于测试 CCXT 库在浏览器环境中的兼容性</li>
                <li>如果 CCXT 加载成功，您可以测试各种交易所的功能</li>
                <li>如果加载失败，会显示具体的错误信息</li>
                <li>所有操作都在浏览器控制台中有详细日志</li>
            </ul>
        </div>

        <div class="controls">
            <button onclick="testCCXTBasic()" id="testBasic">测试基础功能</button>
            <button onclick="listAllExchanges()" id="listExchanges">列出所有交易所</button>
            <button onclick="testBinance()" id="testBinance">测试 Binance</button>
            <button onclick="testOKX()" id="testOKX">测试 OKX</button>
            <button onclick="testBybit()" id="testBybit">测试 Bybit</button>
            <button onclick="testMultipleExchanges()" id="testMultiple">批量测试交易所</button>
            <button onclick="testRealTimeData()" id="testRealTime">测试实时数据</button>
            <button onclick="exportResults()" id="exportBtn">导出结果</button>
            <button onclick="clearOutput()" id="clearBtn">清空输出</button>
        </div>

        <div id="output" class="output">
            等待测试结果...
        </div>

        <div id="exchangeList" class="exchange-grid" style="display: none;">
            <!-- 交易所列表将在这里显示 -->
        </div>
    </div>

    <!-- 尝试从多个CDN加载CCXT -->
    <script>
        // 尝试从多个CDN加载CCXT
        function loadCCXTFromCDN() {
            const cdnUrls = [
                'https://unpkg.com/ccxt@latest/dist/ccxt.browser.js',
                'https://cdn.jsdelivr.net/npm/ccxt@latest/dist/ccxt.browser.js',
                'https://cdn.skypack.dev/ccxt'
            ];

            let currentIndex = 0;

            function tryNextCDN() {
                if (currentIndex >= cdnUrls.length) {
                    console.error('所有CDN都加载失败');
                    return;
                }

                const script = document.createElement('script');
                script.src = cdnUrls[currentIndex];
                script.onload = function() {
                    console.log(`CCXT从CDN加载成功: ${cdnUrls[currentIndex]}`);
                };
                script.onerror = function() {
                    console.warn(`CDN加载失败: ${cdnUrls[currentIndex]}`);
                    currentIndex++;
                    tryNextCDN();
                };
                document.head.appendChild(script);
            }

            tryNextCDN();
        }

        // 开始加载
        loadCCXTFromCDN();
    </script>
    
    <script>
        let ccxtLoaded = false;
        let output = document.getElementById('output');
        let statusDiv = document.getElementById('status');

        // 日志函数
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = `[${timestamp}] ${message}\n`;
            output.textContent += logEntry;
            output.scrollTop = output.scrollHeight;
            
            console.log(`CCXT Test: ${message}`);
            
            if (type === 'error') {
                console.error(message);
            }
        }

        // 检测CCXT是否加载成功
        function checkCCXT() {
            if (typeof ccxt !== 'undefined') {
                ccxtLoaded = true;
                statusDiv.className = 'status success';
                statusDiv.textContent = '✅ CCXT 库加载成功！可以开始测试。';
                log('CCXT 库加载成功');
                log(`可用的交易所数量: ${ccxt.exchanges.length}`);
                enableButtons();
            } else {
                statusDiv.className = 'status error';
                statusDiv.textContent = '❌ CCXT 库加载失败。请检查网络连接或浏览器兼容性。';
                log('CCXT 库加载失败', 'error');
                disableButtons();
            }
        }

        // 启用按钮
        function enableButtons() {
            const buttons = document.querySelectorAll('button');
            buttons.forEach(btn => {
                if (btn.id !== 'clearBtn') {
                    btn.disabled = false;
                }
            });
        }

        // 禁用按钮
        function disableButtons() {
            const buttons = document.querySelectorAll('button');
            buttons.forEach(btn => {
                if (btn.id !== 'clearBtn') {
                    btn.disabled = true;
                }
            });
        }

        // 测试基础功能
        async function testCCXTBasic() {
            if (!ccxtLoaded) {
                log('CCXT 未加载，无法测试', 'error');
                return;
            }

            log('=== 开始基础功能测试 ===');
            
            try {
                log(`CCXT 版本: ${ccxt.version || '未知'}`);
                log(`支持的交易所总数: ${ccxt.exchanges.length}`);
                log(`前10个交易所: ${ccxt.exchanges.slice(0, 10).join(', ')}`);
                
                // 测试创建交易所实例
                const binance = new ccxt.binance();
                log(`成功创建 Binance 实例: ${binance.name}`);
                log(`Binance ID: ${binance.id}`);
                log(`Binance 国家: ${binance.countries.join(', ')}`);
                
                log('✅ 基础功能测试完成');
            } catch (error) {
                log(`❌ 基础功能测试失败: ${error.message}`, 'error');
            }
        }

        // 列出所有交易所
        async function listAllExchanges() {
            if (!ccxtLoaded) {
                log('CCXT 未加载，无法获取交易所列表', 'error');
                return;
            }

            log('=== 获取所有交易所信息 ===');
            
            try {
                const exchangeList = document.getElementById('exchangeList');
                exchangeList.innerHTML = '';
                exchangeList.style.display = 'grid';
                
                const logoMap = {
                    binance: 'https://user-images.githubusercontent.com/1294454/29604020-d5483cdc-87ee-11e7-94c7-d1a8d9169293.jpg',
                    okx: 'https://user-images.githubusercontent.com/1294454/152485636-38b19e4a-bece-4dec-979a-5982859ffc04.jpg',
                    bybit: 'https://user-images.githubusercontent.com/1294454/165102362-f47a0cea-d0c6-4c6f-b4c8-dcf6a6c7f55e.jpg',
                    coinbase: 'https://user-images.githubusercontent.com/1294454/40811661-b6eceae2-653a-11e8-829e-10b03607bc2c.jpg',
                    kraken: 'https://user-images.githubusercontent.com/1294454/27766599-22709304-5ede-11e7-9de1-9f33732e5509.jpg',
                    huobi: 'https://user-images.githubusercontent.com/1294454/76137448-22748a80-604e-11ea-8069-6e389271911d.jpg',
                    kucoin: 'https://user-images.githubusercontent.com/1294454/57369448-3cc3aa80-7196-11e9-883e-5ebeb35e4f57.jpg',
                    gateio: 'https://user-images.githubusercontent.com/1294454/31784029-0313c702-b509-11e7-9ccc-bc0da6a0e435.jpg'
                };

                const mainExchanges = ccxt.exchanges.slice(0, 20); // 只显示前20个
                
                for (const exchangeId of mainExchanges) {
                    try {
                        const ExchangeClass = ccxt[exchangeId];
                        if (typeof ExchangeClass === 'function') {
                            const exchange = new ExchangeClass();
                            
                            const card = document.createElement('div');
                            card.className = 'exchange-card';
                            
                            const logo = logoMap[exchangeId] || '';
                            const logoImg = logo ? `<img src="${logo}" class="exchange-logo" alt="${exchange.name}" onerror="this.style.display='none'">` : '';
                            
                            card.innerHTML = `
                                ${logoImg}
                                <div class="exchange-name">${exchange.name}</div>
                                <div class="exchange-id">${exchange.id}</div>
                                <div style="font-size: 12px; color: #666; margin-top: 5px;">
                                    国家: ${exchange.countries.join(', ') || '未知'}
                                </div>
                                <div style="font-size: 12px; color: #666;">
                                    现货: ${exchange.has.spot ? '✅' : '❌'} | 
                                    期货: ${exchange.has.future ? '✅' : '❌'}
                                </div>
                            `;
                            
                            exchangeList.appendChild(card);
                        }
                    } catch (error) {
                        log(`创建 ${exchangeId} 实例失败: ${error.message}`, 'error');
                    }
                }
                
                log(`✅ 成功显示 ${mainExchanges.length} 个交易所信息`);
            } catch (error) {
                log(`❌ 获取交易所列表失败: ${error.message}`, 'error');
            }
        }

        // 测试Binance
        async function testBinance() {
            await testExchange('binance', 'Binance');
        }

        // 测试OKX
        async function testOKX() {
            await testExchange('okx', 'OKX');
        }

        // 测试Bybit
        async function testBybit() {
            await testExchange('bybit', 'Bybit');
        }

        // 通用交易所测试函数
        async function testExchange(exchangeId, exchangeName) {
            if (!ccxtLoaded) {
                log('CCXT 未加载，无法测试交易所', 'error');
                return;
            }

            log(`=== 开始测试 ${exchangeName} ===`);
            
            try {
                const ExchangeClass = ccxt[exchangeId];
                if (typeof ExchangeClass !== 'function') {
                    throw new Error(`${exchangeName} 交易所类不存在`);
                }

                const exchange = new ExchangeClass({
                    enableRateLimit: true,
                    timeout: 10000,
                });

                log(`✅ 成功创建 ${exchangeName} 实例`);
                log(`交易所名称: ${exchange.name}`);
                log(`交易所ID: ${exchange.id}`);
                log(`支持国家: ${exchange.countries.join(', ')}`);
                log(`请求限制: ${exchange.rateLimit}ms`);
                log(`支持现货: ${exchange.has.spot ? '是' : '否'}`);
                log(`支持期货: ${exchange.has.future ? '是' : '否'}`);
                log(`支持保证金: ${exchange.has.margin ? '是' : '否'}`);

                // 尝试加载市场数据
                log(`正在加载 ${exchangeName} 市场数据...`);
                const markets = await exchange.loadMarkets();
                const marketCount = Object.keys(markets).length;
                log(`✅ 成功加载 ${marketCount} 个交易对`);
                
                // 显示前5个交易对
                const symbols = Object.keys(markets).slice(0, 5);
                log(`前5个交易对: ${symbols.join(', ')}`);

                // 尝试获取ticker数据（如果支持）
                if (exchange.has.fetchTicker && symbols.length > 0) {
                    try {
                        log(`正在获取 ${symbols[0]} 的ticker数据...`);
                        const ticker = await exchange.fetchTicker(symbols[0]);
                        log(`✅ ${symbols[0]} 最新价格: ${ticker.last}`);
                        log(`24h涨跌幅: ${ticker.percentage ? ticker.percentage.toFixed(2) + '%' : '未知'}`);
                    } catch (tickerError) {
                        log(`⚠️ 获取ticker数据失败: ${tickerError.message}`, 'error');
                    }
                }

                log(`✅ ${exchangeName} 测试完成`);
            } catch (error) {
                log(`❌ ${exchangeName} 测试失败: ${error.message}`, 'error');
            }
        }

        // 批量测试多个交易所
        async function testMultipleExchanges() {
            if (!ccxtLoaded) {
                log('CCXT 未加载，无法批量测试', 'error');
                return;
            }

            log('=== 开始批量测试交易所 ===');
            const testExchanges = ['binance', 'okx', 'bybit', 'coinbase', 'kraken'];
            const results = {};

            for (const exchangeId of testExchanges) {
                try {
                    log(`正在测试 ${exchangeId}...`);
                    const ExchangeClass = ccxt[exchangeId];

                    if (typeof ExchangeClass === 'function') {
                        const exchange = new ExchangeClass({ enableRateLimit: true, timeout: 5000 });
                        const startTime = Date.now();

                        try {
                            const markets = await exchange.loadMarkets();
                            const loadTime = Date.now() - startTime;

                            results[exchangeId] = {
                                status: 'success',
                                marketCount: Object.keys(markets).length,
                                loadTime: loadTime,
                                name: exchange.name
                            };

                            log(`✅ ${exchange.name}: ${Object.keys(markets).length} 个交易对, 加载时间: ${loadTime}ms`);
                        } catch (error) {
                            results[exchangeId] = {
                                status: 'error',
                                error: error.message
                            };
                            log(`❌ ${exchangeId} 加载失败: ${error.message}`, 'error');
                        }
                    } else {
                        log(`❌ ${exchangeId} 交易所类不存在`, 'error');
                    }
                } catch (error) {
                    log(`❌ ${exchangeId} 初始化失败: ${error.message}`, 'error');
                }
            }

            log('=== 批量测试结果汇总 ===');
            for (const [id, result] of Object.entries(results)) {
                if (result.status === 'success') {
                    log(`✅ ${result.name}: ${result.marketCount} 交易对, ${result.loadTime}ms`);
                } else {
                    log(`❌ ${id}: ${result.error}`, 'error');
                }
            }
        }

        // 测试实时数据获取
        async function testRealTimeData() {
            if (!ccxtLoaded) {
                log('CCXT 未加载，无法测试实时数据', 'error');
                return;
            }

            log('=== 开始测试实时数据获取 ===');

            try {
                const binance = new ccxt.binance({ enableRateLimit: true });
                await binance.loadMarkets();

                const symbols = ['BTC/USDT', 'ETH/USDT', 'BNB/USDT'];

                for (const symbol of symbols) {
                    try {
                        if (binance.markets[symbol]) {
                            log(`正在获取 ${symbol} 实时数据...`);
                            const ticker = await binance.fetchTicker(symbol);

                            log(`${symbol}:`);
                            log(`  最新价格: $${ticker.last}`);
                            log(`  24h涨跌: ${ticker.change ? ticker.change.toFixed(2) : 'N/A'}`);
                            log(`  24h涨跌幅: ${ticker.percentage ? ticker.percentage.toFixed(2) + '%' : 'N/A'}`);
                            log(`  24h成交量: ${ticker.baseVolume ? ticker.baseVolume.toFixed(2) : 'N/A'}`);
                            log(`  更新时间: ${new Date(ticker.timestamp).toLocaleString()}`);
                            log('---');
                        } else {
                            log(`⚠️ ${symbol} 交易对不存在`, 'error');
                        }
                    } catch (error) {
                        log(`❌ 获取 ${symbol} 数据失败: ${error.message}`, 'error');
                    }
                }

                log('✅ 实时数据测试完成');
            } catch (error) {
                log(`❌ 实时数据测试失败: ${error.message}`, 'error');
            }
        }

        // 导出测试结果
        function exportResults() {
            const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
            const content = output.textContent;

            const blob = new Blob([content], { type: 'text/plain' });
            const url = URL.createObjectURL(blob);

            const a = document.createElement('a');
            a.href = url;
            a.download = `ccxt-test-results-${timestamp}.txt`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);

            log('✅ 测试结果已导出');
        }

        // 清空输出
        function clearOutput() {
            output.textContent = '';
            document.getElementById('exchangeList').style.display = 'none';
            log('输出已清空');
        }

        // 页面加载完成后检测CCXT
        window.addEventListener('load', function() {
            setTimeout(checkCCXT, 1000); // 等待1秒让CCXT加载
        });

        // 初始化时禁用按钮
        disableButtons();
    </script>
</body>
</html>
