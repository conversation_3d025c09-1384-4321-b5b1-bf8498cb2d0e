# CCXT 集成说明

## 概述

本项目支持两种数据源模式：
1. **模拟数据模式**：使用预生成的模拟数据，适合演示和开发
2. **真实CCXT模式**：使用真实的CCXT库获取实时数据

## 当前状态

由于CCXT库在浏览器环境中存在Node.js依赖兼容性问题，当前主要使用模拟数据模式。但是我们已经实现了混合服务架构，可以在CCXT可用时自动切换到真实数据。

## 浏览器中的CCXT访问

应用启动时会尝试加载CCXT库到全局变量 `window.ccxt`，您可以在浏览器控制台中直接使用：

```javascript
// 查看可用的交易所
console.log(window.ccxt.exchanges)

// 创建交易所实例
const binance = new window.ccxt.binance()

// 获取市场数据
binance.loadMarkets().then(markets => {
  console.log('Binance markets:', Object.keys(markets).slice(0, 10))
})
```

## 解决CCXT兼容性问题的方案

### 方案1：使用CDN版本（推荐用于测试）

在 `public/index.html` 中添加：

```html
<script src="https://unpkg.com/ccxt@latest/dist/ccxt.browser.js"></script>
```

然后在应用中使用：

```javascript
// 在组件中使用
if (window.ccxt) {
  const exchange = new window.ccxt.binance()
  // 使用exchange...
}
```

### 方案2：后端API代理（推荐用于生产）

创建一个Node.js后端服务来处理CCXT调用：

```javascript
// backend/server.js
const ccxt = require('ccxt')
const express = require('express')
const app = express()

app.get('/api/exchanges', async (req, res) => {
  try {
    const exchanges = ccxt.exchanges.map(id => {
      const exchange = new ccxt[id]()
      return {
        id: exchange.id,
        name: exchange.name,
        countries: exchange.countries,
        // ... 其他信息
      }
    })
    res.json(exchanges)
  } catch (error) {
    res.status(500).json({ error: error.message })
  }
})

app.listen(3001)
```

然后在前端调用API：

```typescript
// 更新 hybridCcxtService.ts
async getAllExchanges(): Promise<ApiResponse<Exchange[]>> {
  try {
    const response = await fetch('/api/exchanges')
    const data = await response.json()
    return { success: true, data }
  } catch (error) {
    // 回退到模拟数据
    return mockCcxtService.getAllExchanges()
  }
}
```

### 方案3：使用Web Workers

将CCXT操作移到Web Worker中：

```javascript
// worker.js
importScripts('https://unpkg.com/ccxt@latest/dist/ccxt.browser.js')

self.onmessage = async function(e) {
  const { action, exchangeId, symbol } = e.data
  
  try {
    const exchange = new ccxt[exchangeId]()
    
    switch (action) {
      case 'loadMarkets':
        const markets = await exchange.loadMarkets()
        self.postMessage({ success: true, data: markets })
        break
      case 'fetchTicker':
        const ticker = await exchange.fetchTicker(symbol)
        self.postMessage({ success: true, data: ticker })
        break
    }
  } catch (error) {
    self.postMessage({ success: false, error: error.message })
  }
}
```

## 当前实现的功能

### 混合服务架构

`hybridCcxtService.ts` 实现了智能切换：

1. **启动时检测**：检查 `window.ccxt` 是否可用
2. **动态导入**：尝试动态导入CCXT模块
3. **优雅降级**：如果CCXT不可用，自动使用模拟数据
4. **控制台提示**：在控制台显示当前使用的数据源

### 模拟数据特性

当前的模拟数据包括：

- **15+个主流交易所**：完整的交易所信息
- **真实的Logo和链接**：可点击跳转到官网
- **USDT等值计算**：自动换算交易量
- **随机但合理的数据**：价格、交易量等数据

## 开发建议

### 本地开发

1. 使用模拟数据进行UI开发和测试
2. 在浏览器控制台测试CCXT功能
3. 使用混合服务确保代码兼容性

### 生产部署

1. **推荐**：使用后端API代理方案
2. **备选**：使用CDN版本 + 错误处理
3. **保留**：模拟数据作为fallback

## 故障排除

### 常见问题

1. **"Failed to resolve import" 错误**
   - 这是正常的，表示CCXT在浏览器中不兼容
   - 应用会自动使用模拟数据

2. **网络请求失败**
   - 检查CORS设置
   - 确认API端点可访问
   - 查看浏览器控制台错误信息

3. **数据加载缓慢**
   - 真实CCXT调用可能较慢
   - 考虑添加缓存机制
   - 使用分页和懒加载

### 调试技巧

```javascript
// 在控制台中检查服务状态
console.log('CCXT available:', !!window.ccxt)
console.log('Service type:', window.ccxt ? 'Real CCXT' : 'Mock Data')

// 测试特定交易所
if (window.ccxt) {
  const binance = new window.ccxt.binance()
  binance.loadMarkets().then(console.log).catch(console.error)
}
```

## 未来改进

1. **WebSocket支持**：实时价格更新
2. **缓存机制**：减少API调用
3. **错误重试**：自动重试失败的请求
4. **性能优化**：虚拟滚动、懒加载
5. **更多交易所**：支持更多小众交易所
