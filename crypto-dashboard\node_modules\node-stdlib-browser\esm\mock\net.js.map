{"version": 3, "file": "net.js", "sources": ["../../mock/net.js"], "sourcesContent": ["function noop() {}\nfunction bool() {\n\treturn true;\n}\n\nexport {\n\tnoop as createServer,\n\tnoop as createConnection,\n\tnoop as connect,\n\tbool as isIP,\n\tbool as isIPv4,\n\tbool as isIPv6\n};\n"], "names": ["noop", "bool"], "mappings": "AAAA,SAASA,IAAIA,GAAG,EAAC;AACjB,SAASC,IAAIA,GAAG;AACf,EAAA,OAAO,IAAI,CAAA;AACZ;;;;"}