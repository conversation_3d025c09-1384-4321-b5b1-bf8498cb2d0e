{"version": 3, "file": "dns.js", "sources": ["../../mock/dns.js"], "sourcesContent": ["/* globals unknown */\n\n/**\n * @param {unknown[]} arguments_\n */\nconst api = function (...arguments_) {\n\tif (arguments_.length === 0) {\n\t\treturn;\n\t}\n\tconst callback = arguments_[arguments_.length - 1];\n\tif (typeof callback === 'function') {\n\t\tcallback(null, '0.0.0.0');\n\t}\n};\n\nexport {\n\tapi as lookup,\n\tapi as resolve4,\n\tapi as resolve6,\n\tapi as resolveCname,\n\tapi as resolveMx,\n\tapi as resolveNs,\n\tapi as resolveTxt,\n\tapi as resolveSrv,\n\tapi as resolveNaptr,\n\tapi as reverse,\n\tapi as resolve\n};\n"], "names": ["api", "_ref", "arguments", "length", "callback", "undefined"], "mappings": "AAAA;;AAEA;AACA;AACA;AACA,IAAMA,GAAG,GAAG,SAANA,GAAGA,GAA4B;AAAA,EAAA,IAAAC,IAAA,CAAA;AACpC,EAAA,IAAIC,SAAA,CAAWC,MAAM,KAAK,CAAC,EAAE;AAC5B,IAAA,OAAA;AACD,GAAA;EACA,IAAMC,QAAQ,IAAAH,IAAA,GAAcC,SAAA,CAAWC,MAAM,GAAG,CAAC,EAAAF,IAAA,QAAAC,SAAA,CAAAC,MAAA,IAAAF,IAAA,GAAAI,SAAA,GAAAH,SAAA,CAAAD,IAAA,CAAC,CAAA,CAAA;AAClD,EAAA,IAAI,OAAOG,QAAQ,KAAK,UAAU,EAAE;AACnCA,IAAAA,QAAQ,CAAC,IAAI,EAAE,SAAS,CAAC,CAAA;AAC1B,GAAA;AACD;;;;"}