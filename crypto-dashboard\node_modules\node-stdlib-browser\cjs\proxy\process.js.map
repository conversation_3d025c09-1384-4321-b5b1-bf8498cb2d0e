{"version": 3, "file": "process.js", "sources": ["../../node_modules/process/browser.js", "../../proxy/process.js"], "sourcesContent": ["// shim for using process in browser\nvar process = module.exports = {};\n\n// cached from whatever global is present so that test runners that stub it\n// don't break things.  But we need to wrap it in a try catch in case it is\n// wrapped in strict mode code which doesn't define any globals.  It's inside a\n// function because try/catches deoptimize in certain engines.\n\nvar cachedSetTimeout;\nvar cachedClearTimeout;\n\nfunction defaultSetTimout() {\n    throw new Error('setTimeout has not been defined');\n}\nfunction defaultClearTimeout () {\n    throw new Error('clearTimeout has not been defined');\n}\n(function () {\n    try {\n        if (typeof setTimeout === 'function') {\n            cachedSetTimeout = setTimeout;\n        } else {\n            cachedSetTimeout = defaultSetTimout;\n        }\n    } catch (e) {\n        cachedSetTimeout = defaultSetTimout;\n    }\n    try {\n        if (typeof clearTimeout === 'function') {\n            cachedClearTimeout = clearTimeout;\n        } else {\n            cachedClearTimeout = defaultClearTimeout;\n        }\n    } catch (e) {\n        cachedClearTimeout = defaultClearTimeout;\n    }\n} ())\nfunction runTimeout(fun) {\n    if (cachedSetTimeout === setTimeout) {\n        //normal enviroments in sane situations\n        return setTimeout(fun, 0);\n    }\n    // if setTimeout wasn't available but was latter defined\n    if ((cachedSetTimeout === defaultSetTimout || !cachedSetTimeout) && setTimeout) {\n        cachedSetTimeout = setTimeout;\n        return setTimeout(fun, 0);\n    }\n    try {\n        // when when somebody has screwed with setTimeout but no I.E. maddness\n        return cachedSetTimeout(fun, 0);\n    } catch(e){\n        try {\n            // When we are in I.E. but the script has been evaled so I.E. doesn't trust the global object when called normally\n            return cachedSetTimeout.call(null, fun, 0);\n        } catch(e){\n            // same as above but when it's a version of I.E. that must have the global object for 'this', hopfully our context correct otherwise it will throw a global error\n            return cachedSetTimeout.call(this, fun, 0);\n        }\n    }\n\n\n}\nfunction runClearTimeout(marker) {\n    if (cachedClearTimeout === clearTimeout) {\n        //normal enviroments in sane situations\n        return clearTimeout(marker);\n    }\n    // if clearTimeout wasn't available but was latter defined\n    if ((cachedClearTimeout === defaultClearTimeout || !cachedClearTimeout) && clearTimeout) {\n        cachedClearTimeout = clearTimeout;\n        return clearTimeout(marker);\n    }\n    try {\n        // when when somebody has screwed with setTimeout but no I.E. maddness\n        return cachedClearTimeout(marker);\n    } catch (e){\n        try {\n            // When we are in I.E. but the script has been evaled so I.E. doesn't  trust the global object when called normally\n            return cachedClearTimeout.call(null, marker);\n        } catch (e){\n            // same as above but when it's a version of I.E. that must have the global object for 'this', hopfully our context correct otherwise it will throw a global error.\n            // Some versions of I.E. have different rules for clearTimeout vs setTimeout\n            return cachedClearTimeout.call(this, marker);\n        }\n    }\n\n\n\n}\nvar queue = [];\nvar draining = false;\nvar currentQueue;\nvar queueIndex = -1;\n\nfunction cleanUpNextTick() {\n    if (!draining || !currentQueue) {\n        return;\n    }\n    draining = false;\n    if (currentQueue.length) {\n        queue = currentQueue.concat(queue);\n    } else {\n        queueIndex = -1;\n    }\n    if (queue.length) {\n        drainQueue();\n    }\n}\n\nfunction drainQueue() {\n    if (draining) {\n        return;\n    }\n    var timeout = runTimeout(cleanUpNextTick);\n    draining = true;\n\n    var len = queue.length;\n    while(len) {\n        currentQueue = queue;\n        queue = [];\n        while (++queueIndex < len) {\n            if (currentQueue) {\n                currentQueue[queueIndex].run();\n            }\n        }\n        queueIndex = -1;\n        len = queue.length;\n    }\n    currentQueue = null;\n    draining = false;\n    runClearTimeout(timeout);\n}\n\nprocess.nextTick = function (fun) {\n    var args = new Array(arguments.length - 1);\n    if (arguments.length > 1) {\n        for (var i = 1; i < arguments.length; i++) {\n            args[i - 1] = arguments[i];\n        }\n    }\n    queue.push(new Item(fun, args));\n    if (queue.length === 1 && !draining) {\n        runTimeout(drainQueue);\n    }\n};\n\n// v8 likes predictible objects\nfunction Item(fun, array) {\n    this.fun = fun;\n    this.array = array;\n}\nItem.prototype.run = function () {\n    this.fun.apply(null, this.array);\n};\nprocess.title = 'browser';\nprocess.browser = true;\nprocess.env = {};\nprocess.argv = [];\nprocess.version = ''; // empty string to avoid regexp issues\nprocess.versions = {};\n\nfunction noop() {}\n\nprocess.on = noop;\nprocess.addListener = noop;\nprocess.once = noop;\nprocess.off = noop;\nprocess.removeListener = noop;\nprocess.removeAllListeners = noop;\nprocess.emit = noop;\nprocess.prependListener = noop;\nprocess.prependOnceListener = noop;\n\nprocess.listeners = function (name) { return [] }\n\nprocess.binding = function (name) {\n    throw new Error('process.binding is not supported');\n};\n\nprocess.cwd = function () { return '/' };\nprocess.chdir = function (dir) {\n    throw new Error('process.chdir is not supported');\n};\nprocess.umask = function() { return 0; };\n", "import {\n\tnextTick,\n\ttitle,\n\tenv as environment,\n\targv,\n\tversion,\n\tversions,\n\ton,\n\taddListener,\n\tonce,\n\toff,\n\tremoveListener,\n\tremoveAllListeners,\n\temit,\n\tprependListener,\n\tprependOnceListener,\n\tlisteners,\n\tcwd,\n\tchdir,\n\tumask,\n\t// @ts-ignore\n\tbrowser as _browser,\n\t// @ts-ignore\n\tbinding as _binding\n} from 'process/browser.js';\n\nfunction noop() {}\n\nconst browser = /** @type {boolean} */ (_browser);\nconst emitWarning = noop;\nconst binding = /** @type {Function} */ (_binding);\nconst exit = noop;\nconst pid = 1;\nconst features = {};\nconst kill = noop;\nconst dlopen = noop;\nconst uptime = noop;\nconst memoryUsage = noop;\nconst uvCounters = noop;\nconst platform = 'browser';\nconst arch = 'browser';\nconst execPath = 'browser';\nconst execArgv = /** @type {string[]} */ ([]);\n\nconst api = {\n\tnextTick,\n\ttitle,\n\tbrowser,\n\tenv: environment,\n\targv,\n\tversion,\n\tversions,\n\ton,\n\taddListener,\n\tonce,\n\toff,\n\tremoveListener,\n\tremoveAllListeners,\n\temit,\n\temitWarning,\n\tprependListener,\n\tprependOnceListener,\n\tlisteners,\n\tbinding,\n\tcwd,\n\tchdir,\n\tumask,\n\texit,\n\tpid,\n\tfeatures,\n\tkill,\n\tdlopen,\n\tuptime,\n\tmemoryUsage,\n\tuvCounters,\n\tplatform,\n\tarch,\n\texecPath,\n\texecArgv\n};\n\nexport default api;\n\nexport {\n\tnextTick,\n\ttitle,\n\tbrowser,\n\tenvironment as env,\n\targv,\n\tversion,\n\tversions,\n\ton,\n\taddListener,\n\tonce,\n\toff,\n\tremoveListener,\n\tremoveAllListeners,\n\temit,\n\temitWarning,\n\tprependListener,\n\tprependOnceListener,\n\tlisteners,\n\tbinding,\n\tcwd,\n\tchdir,\n\tumask,\n\texit,\n\tpid,\n\tfeatures,\n\tkill,\n\tdlopen,\n\tuptime,\n\tmemoryUsage,\n\tuvCounters,\n\tplatform,\n\tarch,\n\texecPath,\n\texecArgv\n};\n"], "names": ["browserModule", "noop", "browser", "_browser", "emitWarning", "binding", "_binding", "exit", "pid", "features", "kill", "dlopen", "uptime", "memoryUsage", "uvCounters", "platform", "arch", "execPath", "execArgv", "api", "nextTick", "title", "env", "environment", "argv", "version", "versions", "on", "addListener", "once", "off", "removeListener", "removeAllListeners", "emit", "prependListener", "prependOnceListener", "listeners", "cwd", "chdir", "umask"], "mappings": ";;;;;;AAAA;AACA,IAAI,OAAO,GAAGA,SAAc,CAAA,OAAA,GAAG,EAAE,CAAC;AAClC;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,gBAAgB,CAAC;AACrB,IAAI,kBAAkB,CAAC;AACvB;AACA,SAAS,gBAAgB,GAAG;AAC5B,IAAI,MAAM,IAAI,KAAK,CAAC,iCAAiC,CAAC,CAAC;AACvD,CAAC;AACD,SAAS,mBAAmB,IAAI;AAChC,IAAI,MAAM,IAAI,KAAK,CAAC,mCAAmC,CAAC,CAAC;AACzD,CAAC;AACD,CAAC,YAAY;AACb,IAAI,IAAI;AACR,QAAQ,IAAI,OAAO,UAAU,KAAK,UAAU,EAAE;AAC9C,YAAY,gBAAgB,GAAG,UAAU,CAAC;AAC1C,SAAS,MAAM;AACf,YAAY,gBAAgB,GAAG,gBAAgB,CAAC;AAChD,SAAS;AACT,KAAK,CAAC,OAAO,CAAC,EAAE;AAChB,QAAQ,gBAAgB,GAAG,gBAAgB,CAAC;AAC5C,KAAK;AACL,IAAI,IAAI;AACR,QAAQ,IAAI,OAAO,YAAY,KAAK,UAAU,EAAE;AAChD,YAAY,kBAAkB,GAAG,YAAY,CAAC;AAC9C,SAAS,MAAM;AACf,YAAY,kBAAkB,GAAG,mBAAmB,CAAC;AACrD,SAAS;AACT,KAAK,CAAC,OAAO,CAAC,EAAE;AAChB,QAAQ,kBAAkB,GAAG,mBAAmB,CAAC;AACjD,KAAK;AACL,CAAC,GAAG,EAAC;AACL,SAAS,UAAU,CAAC,GAAG,EAAE;AACzB,IAAI,IAAI,gBAAgB,KAAK,UAAU,EAAE;AACzC;AACA,QAAQ,OAAO,UAAU,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;AAClC,KAAK;AACL;AACA,IAAI,IAAI,CAAC,gBAAgB,KAAK,gBAAgB,IAAI,CAAC,gBAAgB,KAAK,UAAU,EAAE;AACpF,QAAQ,gBAAgB,GAAG,UAAU,CAAC;AACtC,QAAQ,OAAO,UAAU,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;AAClC,KAAK;AACL,IAAI,IAAI;AACR;AACA,QAAQ,OAAO,gBAAgB,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;AACxC,KAAK,CAAC,MAAM,CAAC,CAAC;AACd,QAAQ,IAAI;AACZ;AACA,YAAY,OAAO,gBAAgB,CAAC,IAAI,CAAC,IAAI,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC;AACvD,SAAS,CAAC,MAAM,CAAC,CAAC;AAClB;AACA,YAAY,OAAO,gBAAgB,CAAC,IAAI,CAAC,IAAI,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC;AACvD,SAAS;AACT,KAAK;AACL;AACA;AACA,CAAC;AACD,SAAS,eAAe,CAAC,MAAM,EAAE;AACjC,IAAI,IAAI,kBAAkB,KAAK,YAAY,EAAE;AAC7C;AACA,QAAQ,OAAO,YAAY,CAAC,MAAM,CAAC,CAAC;AACpC,KAAK;AACL;AACA,IAAI,IAAI,CAAC,kBAAkB,KAAK,mBAAmB,IAAI,CAAC,kBAAkB,KAAK,YAAY,EAAE;AAC7F,QAAQ,kBAAkB,GAAG,YAAY,CAAC;AAC1C,QAAQ,OAAO,YAAY,CAAC,MAAM,CAAC,CAAC;AACpC,KAAK;AACL,IAAI,IAAI;AACR;AACA,QAAQ,OAAO,kBAAkB,CAAC,MAAM,CAAC,CAAC;AAC1C,KAAK,CAAC,OAAO,CAAC,CAAC;AACf,QAAQ,IAAI;AACZ;AACA,YAAY,OAAO,kBAAkB,CAAC,IAAI,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;AACzD,SAAS,CAAC,OAAO,CAAC,CAAC;AACnB;AACA;AACA,YAAY,OAAO,kBAAkB,CAAC,IAAI,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;AACzD,SAAS;AACT,KAAK;AACL;AACA;AACA;AACA,CAAC;AACD,IAAI,KAAK,GAAG,EAAE,CAAC;AACf,IAAI,QAAQ,GAAG,KAAK,CAAC;AACrB,IAAI,YAAY,CAAC;AACjB,IAAI,UAAU,GAAG,CAAC,CAAC,CAAC;AACpB;AACA,SAAS,eAAe,GAAG;AAC3B,IAAI,IAAI,CAAC,QAAQ,IAAI,CAAC,YAAY,EAAE;AACpC,QAAQ,OAAO;AACf,KAAK;AACL,IAAI,QAAQ,GAAG,KAAK,CAAC;AACrB,IAAI,IAAI,YAAY,CAAC,MAAM,EAAE;AAC7B,QAAQ,KAAK,GAAG,YAAY,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;AAC3C,KAAK,MAAM;AACX,QAAQ,UAAU,GAAG,CAAC,CAAC,CAAC;AACxB,KAAK;AACL,IAAI,IAAI,KAAK,CAAC,MAAM,EAAE;AACtB,QAAQ,UAAU,EAAE,CAAC;AACrB,KAAK;AACL,CAAC;AACD;AACA,SAAS,UAAU,GAAG;AACtB,IAAI,IAAI,QAAQ,EAAE;AAClB,QAAQ,OAAO;AACf,KAAK;AACL,IAAI,IAAI,OAAO,GAAG,UAAU,CAAC,eAAe,CAAC,CAAC;AAC9C,IAAI,QAAQ,GAAG,IAAI,CAAC;AACpB;AACA,IAAI,IAAI,GAAG,GAAG,KAAK,CAAC,MAAM,CAAC;AAC3B,IAAI,MAAM,GAAG,EAAE;AACf,QAAQ,YAAY,GAAG,KAAK,CAAC;AAC7B,QAAQ,KAAK,GAAG,EAAE,CAAC;AACnB,QAAQ,OAAO,EAAE,UAAU,GAAG,GAAG,EAAE;AACnC,YAAY,IAAI,YAAY,EAAE;AAC9B,gBAAgB,YAAY,CAAC,UAAU,CAAC,CAAC,GAAG,EAAE,CAAC;AAC/C,aAAa;AACb,SAAS;AACT,QAAQ,UAAU,GAAG,CAAC,CAAC,CAAC;AACxB,QAAQ,GAAG,GAAG,KAAK,CAAC,MAAM,CAAC;AAC3B,KAAK;AACL,IAAI,YAAY,GAAG,IAAI,CAAC;AACxB,IAAI,QAAQ,GAAG,KAAK,CAAC;AACrB,IAAI,eAAe,CAAC,OAAO,CAAC,CAAC;AAC7B,CAAC;AACD;AACA,OAAO,CAAC,QAAQ,GAAG,UAAU,GAAG,EAAE;AAClC,IAAI,IAAI,IAAI,GAAG,IAAI,KAAK,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;AAC/C,IAAI,IAAI,SAAS,CAAC,MAAM,GAAG,CAAC,EAAE;AAC9B,QAAQ,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,SAAS,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AACnD,YAAY,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,SAAS,CAAC,CAAC,CAAC,CAAC;AACvC,SAAS;AACT,KAAK;AACL,IAAI,KAAK,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC,CAAC;AACpC,IAAI,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC,IAAI,CAAC,QAAQ,EAAE;AACzC,QAAQ,UAAU,CAAC,UAAU,CAAC,CAAC;AAC/B,KAAK;AACL,CAAC,CAAC;AACF;AACA;AACA,SAAS,IAAI,CAAC,GAAG,EAAE,KAAK,EAAE;AAC1B,IAAI,IAAI,CAAC,GAAG,GAAG,GAAG,CAAC;AACnB,IAAI,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;AACvB,CAAC;AACD,IAAI,CAAC,SAAS,CAAC,GAAG,GAAG,YAAY;AACjC,IAAI,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC;AACrC,CAAC,CAAC;AACF,OAAO,CAAC,KAAK,GAAG,SAAS,CAAC;AAC1B,OAAO,CAAC,OAAO,GAAG,IAAI,CAAC;AACvB,OAAO,CAAC,GAAG,GAAG,EAAE,CAAC;AACjB,OAAO,CAAC,IAAI,GAAG,EAAE,CAAC;AAClB,OAAO,CAAC,OAAO,GAAG,EAAE,CAAC;AACrB,OAAO,CAAC,QAAQ,GAAG,EAAE,CAAC;AACtB;AACA,SAASC,MAAI,GAAG,EAAE;AAClB;AACA,OAAO,CAAC,EAAE,GAAGA,MAAI,CAAC;AAClB,OAAO,CAAC,WAAW,GAAGA,MAAI,CAAC;AAC3B,OAAO,CAAC,IAAI,GAAGA,MAAI,CAAC;AACpB,OAAO,CAAC,GAAG,GAAGA,MAAI,CAAC;AACnB,OAAO,CAAC,cAAc,GAAGA,MAAI,CAAC;AAC9B,OAAO,CAAC,kBAAkB,GAAGA,MAAI,CAAC;AAClC,OAAO,CAAC,IAAI,GAAGA,MAAI,CAAC;AACpB,OAAO,CAAC,eAAe,GAAGA,MAAI,CAAC;AAC/B,OAAO,CAAC,mBAAmB,GAAGA,MAAI,CAAC;AACnC;AACA,OAAO,CAAC,SAAS,GAAG,UAAU,IAAI,EAAE,EAAE,OAAO,EAAE,GAAE;AACjD;AACA,OAAO,CAAC,OAAO,GAAG,UAAU,IAAI,EAAE;AAClC,IAAI,MAAM,IAAI,KAAK,CAAC,kCAAkC,CAAC,CAAC;AACxD,CAAC,CAAC;AACF;AACA,OAAO,CAAC,GAAG,GAAG,YAAY,EAAE,OAAO,GAAG,EAAE,CAAC;AACzC,OAAO,CAAC,KAAK,GAAG,UAAU,GAAG,EAAE;AAC/B,IAAI,MAAM,IAAI,KAAK,CAAC,gCAAgC,CAAC,CAAC;AACtD,CAAC,CAAC;AACF,OAAO,CAAC,KAAK,GAAG,WAAW,EAAE,OAAO,CAAC,CAAC,EAAE;;AC7JxC,SAASA,IAAIA,GAAG,EAAC;AAEXC,IAAAA,OAAO,yBAA2BC,0BAAS;AAC3CC,IAAAA,WAAW,GAAGH,KAAI;AAClBI,IAAAA,OAAO,0BAA4BC,0BAAS;AAC5CC,IAAAA,IAAI,GAAGN,KAAI;AACXO,IAAAA,GAAG,GAAG,EAAC;AACPC,IAAAA,QAAQ,GAAG,GAAE;AACbC,IAAAA,IAAI,GAAGT,KAAI;AACXU,IAAAA,MAAM,GAAGV,KAAI;AACbW,IAAAA,MAAM,GAAGX,KAAI;AACbY,IAAAA,WAAW,GAAGZ,KAAI;AAClBa,IAAAA,UAAU,GAAGb,KAAI;AACjBc,IAAAA,QAAQ,GAAG,UAAS;AACpBC,IAAAA,IAAI,GAAG,UAAS;AAChBC,IAAAA,QAAQ,GAAG,UAAS;AACpBC,IAAAA,QAAQ,0BAA4B,GAAG;AAE7C,IAAMC,GAAG,GAAG;AACXC,EAAAA,QAAQ,EAARA,0BAAQ;AACRC,EAAAA,KAAK,EAALA,uBAAK;AACLnB,EAAAA,OAAO,EAAPA,OAAO;AACPoB,EAAAA,GAAG,EAAEC,qBAAW;AAChBC,EAAAA,IAAI,EAAJA,sBAAI;AACJC,EAAAA,OAAO,EAAPA,yBAAO;AACPC,EAAAA,QAAQ,EAARA,0BAAQ;AACRC,EAAAA,EAAE,EAAFA,oBAAE;AACFC,EAAAA,WAAW,EAAXA,6BAAW;AACXC,EAAAA,IAAI,EAAJA,sBAAI;AACJC,EAAAA,GAAG,EAAHA,qBAAG;AACHC,EAAAA,cAAc,EAAdA,gCAAc;AACdC,EAAAA,kBAAkB,EAAlBA,oCAAkB;AAClBC,EAAAA,IAAI,EAAJA,sBAAI;AACJ7B,EAAAA,WAAW,EAAXA,WAAW;AACX8B,EAAAA,eAAe,EAAfA,iCAAe;AACfC,EAAAA,mBAAmB,EAAnBA,qCAAmB;AACnBC,EAAAA,SAAS,EAATA,2BAAS;AACT/B,EAAAA,OAAO,EAAPA,OAAO;AACPgC,EAAAA,GAAG,EAAHA,qBAAG;AACHC,EAAAA,KAAK,EAALA,uBAAK;AACLC,EAAAA,KAAK,EAALA,uBAAK;AACLhC,EAAAA,IAAI,EAAJA,IAAI;AACJC,EAAAA,GAAG,EAAHA,GAAG;AACHC,EAAAA,QAAQ,EAARA,QAAQ;AACRC,EAAAA,IAAI,EAAJA,IAAI;AACJC,EAAAA,MAAM,EAANA,MAAM;AACNC,EAAAA,MAAM,EAANA,MAAM;AACNC,EAAAA,WAAW,EAAXA,WAAW;AACXC,EAAAA,UAAU,EAAVA,UAAU;AACVC,EAAAA,QAAQ,EAARA,QAAQ;AACRC,EAAAA,IAAI,EAAJA,IAAI;AACJC,EAAAA,QAAQ,EAARA,QAAQ;AACRC,EAAAA,QAAQ,EAARA,QAAAA;AACD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;"}