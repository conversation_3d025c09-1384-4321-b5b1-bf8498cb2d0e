import { createApp } from 'vue'
import App from './App.vue'
import router from './router'
import pinia from './stores'
import ElementPlus from 'element-plus'
import 'element-plus/dist/index.css'
import * as ElementPlusIconsVue from '@element-plus/icons-vue'

// 尝试导入CCXT，如果失败则在控制台显示警告
try {
  const _ccxt = await import('ccxt')
  ;(window as any).ccxt = _ccxt.default || _ccxt
  console.log('CCXT loaded successfully. You can access it via window.ccxt')
  console.log('Available exchanges:', Object.keys((window as any).ccxt.exchanges || {}))
} catch (error) {
  console.warn('CCXT failed to load in browser environment:', error)
  console.log('Using mock data for demonstration. In production, consider using a backend API.')
}
const app = createApp(App)

// 注册Element Plus图标
for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
  app.component(key, component)
}

app.use(router)
app.use(pinia)
app.use(ElementPlus)

app.mount('#app')
