{"name": "isomorphic-timers-promises", "version": "1.0.1", "description": "`timers/promises` for client and server.", "license": "MIT", "author": "<PERSON> <<EMAIL>> (http://ivannikolic.com)", "sideEffects": false, "exports": {".": {"import": "./esm/index.js", "require": "./cjs/index.js"}, "./package.json": "./package.json"}, "main": "cjs/index.js", "module": "esm/index.js", "directories": {"test": "test"}, "files": ["cjs/", "esm/", "CHANGELOG.md", "LICENSE.md", "README.md"], "scripts": {"release": "np --no-release-draft", "version": "if [ $(git rev-parse --abbrev-ref HEAD) == 'master' ]; then version-changelog CHANGELOG.md && changelog-verify CHANGELOG.md && git add CHANGELOG.md; else echo; fi", "postpublish": "GITHUB_TOKEN=$GITHUB_RELEASE_TOKEN github-release-from-changelog", "lint": "eslint '{index,lib/**/*,test/**/*}.js'", "test:automated": "BABEL_ENV=test karma start", "test:automated:watch": "npm run test:automated -- --auto-watch --no-single-run", "test": "npm run lint && npm run test:automated", "build": "rollup --config rollup.config.js", "module-check": "node -e 'require(\"isomorphic-timers-promises\");' && node --input-type=module -e 'import \"isomorphic-timers-promises\";'", "prepublishOnly": "npm run build && npm run module-check"}, "devDependencies": {"@babel/cli": "^7.2.3", "@babel/core": "^7.2.2", "@babel/plugin-transform-member-expression-literals": "^7.12.1", "@babel/plugin-transform-object-assign": "^7.2.0", "@babel/plugin-transform-property-literals": "^7.12.1", "@babel/plugin-transform-runtime": "^7.2.0", "@babel/preset-env": "^7.12.1", "@babel/runtime": "^7.2.0", "@rollup/plugin-babel": "^5.2.1", "@rollup/plugin-commonjs": "^15.1.0", "@rollup/plugin-node-resolve": "^9.0.0", "abortcontroller-polyfill": "^1.7.3", "babel-plugin-native-error-extend": "^2.0.2", "babel-plugin-transform-globalthis": "^1.0.0", "changelog-verify": "^1.1.2", "core-js": "^2.6.5", "eslint": "^7.11.0", "eslint-config-niksy": "^9.0.0", "eslint-config-prettier": "^6.14.0", "eslint-plugin-extend": "^0.1.1", "eslint-plugin-import": "^2.22.1", "eslint-plugin-jsdoc": "^30.7.3", "eslint-plugin-mocha": "^8.0.0", "eslint-plugin-node": "^11.1.0", "eslint-plugin-prettier": "^3.0.1", "eslint-plugin-promise": "^4.1.1", "eslint-plugin-unicorn": "^23.0.0", "esm": "^3.0.51", "github-release-from-changelog": "^2.1.1", "husky": "^4.3.0", "karma": "^5.2.3", "karma-browserstack-launcher": "^1.6.0", "karma-chrome-launcher": "^3.1.0", "karma-coverage-istanbul-reporter": "^3.0.3", "karma-fixture": "^0.2.6", "karma-html2js-preprocessor": "^1.1.0", "karma-mocha": "^2.0.1", "karma-mocha-reporter": "^2.2.5", "karma-rollup-preprocessor": "^7.0.0", "karma-sourcemap-loader": "^0.3.7", "lint-staged": "^10.4.2", "mocha": "^8.2.0", "np": "^6.5.0", "prettier": "^2.1.2", "rollup": "^2.32.1", "rollup-plugin-istanbul": "^3.0.0", "rollup-plugin-node-builtins": "^2.1.2", "rollup-plugin-node-globals": "^1.4.0", "set-immediate-shim": "^2.0.0", "version-changelog": "^3.1.1"}, "engines": {"node": ">=10"}, "keywords": ["timers", "promises", "async", "promise", "settimeout", "setinterval", "setimmediate"], "repository": {"type": "git", "url": "git+https://github.com/niksy/isomorphic-timers-promises.git"}, "bugs": {"url": "https://github.com/niksy/isomorphic-timers-promises/issues"}, "homepage": "https://github.com/niksy/isomorphic-timers-promises#readme"}