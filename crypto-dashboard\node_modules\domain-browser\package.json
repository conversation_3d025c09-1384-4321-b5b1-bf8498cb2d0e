{"name": "domain-browser", "version": "4.22.0", "description": "<PERSON>de's domain module for the web browser. This is merely an evented try...catch with the same API as node, nothing more.", "homepage": "https://github.com/bevry/domain-browser", "license": "MIT", "keywords": ["amd", "browser", "catch", "component", "component.io", "domain", "ender.js", "es5", "node", "node-compat", "require.js", "try", "trycatch", "umd"], "badges": {"list": ["githubworkflow", "npmversion", "npmdownloads", "da<PERSON><PERSON><PERSON>", "<PERSON><PERSON>dm<PERSON>v", "---", "githubsponsors", "patreon", "flattr", "liberapay", "buymeacoffee", "opencollective", "crypto", "paypal", "wishlist"], "config": {"githubWorkflow": "bevry", "githubSponsorsUsername": "bal<PERSON><PERSON>", "buymeacoffeeUsername": "bal<PERSON><PERSON>", "cryptoURL": "https://bevry.me/crypto", "flattrUsername": "bal<PERSON><PERSON>", "liberapayUsername": "bevry", "opencollectiveUsername": "bevry", "patreonUsername": "bevry", "paypalURL": "https://bevry.me/paypal", "wishlistURL": "https://bevry.me/wishlist", "githubUsername": "bevry", "githubRepository": "domain-browser", "githubSlug": "bevry/domain-browser", "npmPackageName": "domain-browser"}}, "funding": "https://bevry.me/fund", "author": "2013+ Bevry Pty Ltd <<EMAIL>> (http://bevry.me)", "maintainers": ["<PERSON> <<EMAIL>> (https://github.com/balupton)"], "contributors": ["<PERSON> <<EMAIL>> (https://github.com/balupton)", "<PERSON><PERSON><PERSON> <<EMAIL>> (https://github.com/TrySound)", "<PERSON> (https://github.com/evansolomon)", "<PERSON> <<EMAIL>> (https://github.com/guybedford)", "<PERSON> <<EMAIL>> (https://github.com/substack)"], "bugs": {"url": "https://github.com/bevry/domain-browser/issues"}, "repository": {"type": "git", "url": "https://github.com/bevry/domain-browser.git"}, "engines": {"node": ">=10"}, "editions": [{"description": "ES5 source code for web browsers and Node.js with Require for modules", "directory": "source", "entry": "index.js", "tags": ["source", "javascript", "es5", "require"], "engines": {"node": "10 || 12 || 14 || 16", "browsers": "defaults"}}], "type": "commonjs", "main": "source/index.js", "browser": "source/index.js", "jspm": {"map": {"source/index.js": {"node": "@node/domain"}}}, "devDependencies": {"@bevry/update-contributors": "^1.19.0", "assert-helpers": "^8.4.0", "kava": "^5.14.0", "projectz": "^2.21.0", "valid-directory": "^3.7.0"}, "scripts": {"our:clean": "rm -Rf ./docs ./edition* ./es2015 ./es5 ./out ./.next", "our:compile": "echo no need for this project", "our:deploy": "echo no need for this project", "our:meta": "npm run our:meta:contributors && npm run our:meta:projectz", "our:meta:contributors": "update-contributors", "our:meta:projectz": "projectz compile", "our:release": "npm run our:release:prepare && npm run our:release:check-changelog && npm run our:release:check-dirty && npm run our:release:tag && npm run our:release:push", "our:release:check-changelog": "cat ./HISTORY.md | grep v$npm_package_version || (echo add a changelog entry for v$npm_package_version && exit -1)", "our:release:check-dirty": "git diff --exit-code", "our:release:prepare": "npm run our:clean && npm run our:compile && npm run our:test && npm run our:meta", "our:release:push": "git push origin && git push origin --tags", "our:release:tag": "export MESSAGE=$(cat ./HISTORY.md | sed -n \"/## v$npm_package_version/,/##/p\" | sed 's/## //' | awk 'NR>1{print buf}{buf = $0}') && test \"$MESSAGE\" || (echo 'proper changelog entry not found' && exit -1) && git tag v$npm_package_version -am \"$MESSAGE\"", "our:setup": "npm run our:setup:install", "our:setup:install": "npm install", "our:test": "npm run our:verify && npm test", "our:verify": "npm run our:verify:directory", "our:verify:directory": "valid-directory", "test": "node ./source/test.js"}}