/* globals unknown */

/**
 * @param {unknown[]} arguments_
 */
var api = function api() {
  var _ref;
  if (arguments.length === 0) {
    return;
  }
  var callback = (_ref = arguments.length - 1, _ref < 0 || arguments.length <= _ref ? undefined : arguments[_ref]);
  if (typeof callback === 'function') {
    callback(null, '0.0.0.0');
  }
};

export { api as lookup, api as resolve, api as resolve4, api as resolve6, api as resolveCname, api as resolveMx, api as resolveNaptr, api as resolveNs, api as resolveSrv, api as resolveTxt, api as reverse };
//# sourceMappingURL=dns.js.map
