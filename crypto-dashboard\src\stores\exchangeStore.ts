import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import type { Exchange, Symbol, Ticker, FilterOptions, SortOption, SortDirection, LoadingState, ErrorState } from '@/types'
import ccxtService from '@/services/hybridCcxtService'

export const useExchangeStore = defineStore('exchange', () => {
  // 状态
  const exchanges = ref<Exchange[]>([])
  const symbols = ref<Symbol[]>([])
  const tickers = ref<Map<string, Ticker>>(new Map())
  
  const loading = ref<LoadingState>({
    exchanges: false,
    symbols: false,
    tickers: false
  })
  
  const errors = ref<ErrorState>({
    exchanges: null,
    symbols: null,
    tickers: null
  })

  // 筛选和排序状态
  const filters = ref<FilterOptions>({
    exchangeType: 'all',
    symbolType: 'all',
    search: '',
    activeOnly: true
  })

  const sortBy = ref<SortOption>('name')
  const sortDirection = ref<SortDirection>('asc')

  // 计算属性 - 筛选后的交易所
  const filteredExchanges = computed(() => {
    let result = [...exchanges.value]

    // 按交易类型筛选
    if (filters.value.exchangeType && filters.value.exchangeType !== 'all') {
      result = result.filter(exchange => {
        switch (filters.value.exchangeType) {
          case 'spot':
            return exchange.spot
          case 'future':
            return exchange.future
          case 'margin':
            return exchange.margin
          case 'option':
            return exchange.option
          default:
            return true
        }
      })
    }

    // 按搜索关键词筛选
    if (filters.value.search) {
      const searchTerm = filters.value.search.toLowerCase()
      result = result.filter(exchange => 
        exchange.name.toLowerCase().includes(searchTerm) ||
        exchange.id.toLowerCase().includes(searchTerm) ||
        exchange.countries.some(country => country.toLowerCase().includes(searchTerm))
      )
    }

    return result
  })

  // 计算属性 - 排序后的交易所
  const sortedExchanges = computed(() => {
    const result = [...filteredExchanges.value]
    
    result.sort((a, b) => {
      let aValue: any
      let bValue: any

      switch (sortBy.value) {
        case 'name':
          aValue = a.name
          bValue = b.name
          break
        case 'volume24h':
          aValue = a.volume24h || 0
          bValue = b.volume24h || 0
          break
        case 'countries':
          aValue = a.countries.length
          bValue = b.countries.length
          break
        case 'rateLimit':
          aValue = a.rateLimit
          bValue = b.rateLimit
          break
        default:
          return 0
      }

      if (typeof aValue === 'string' && typeof bValue === 'string') {
        const comparison = aValue.localeCompare(bValue)
        return sortDirection.value === 'asc' ? comparison : -comparison
      } else {
        const comparison = (aValue || 0) - (bValue || 0)
        return sortDirection.value === 'asc' ? comparison : -comparison
      }
    })

    return result
  })

  // 计算属性 - 筛选后的交易对
  const filteredSymbols = computed(() => {
    let result = [...symbols.value]

    // 按交易类型筛选
    if (filters.value.symbolType && filters.value.symbolType !== 'all') {
      result = result.filter(symbol => {
        switch (filters.value.symbolType) {
          case 'spot':
            return symbol.spot
          case 'future':
            return symbol.future
          case 'margin':
            return symbol.margin
          case 'option':
            return symbol.option
          default:
            return true
        }
      })
    }

    // 只显示活跃的交易对
    if (filters.value.activeOnly) {
      result = result.filter(symbol => symbol.active)
    }

    // 按搜索关键词筛选
    if (filters.value.search) {
      const searchTerm = filters.value.search.toLowerCase()
      result = result.filter(symbol => 
        symbol.symbol.toLowerCase().includes(searchTerm) ||
        symbol.base.toLowerCase().includes(searchTerm) ||
        symbol.quote.toLowerCase().includes(searchTerm) ||
        symbol.exchangeName.toLowerCase().includes(searchTerm)
      )
    }

    return result
  })

  // 计算属性 - 排序后的交易对
  const sortedSymbols = computed(() => {
    const result = [...filteredSymbols.value]
    
    result.sort((a, b) => {
      let aValue: any
      let bValue: any

      switch (sortBy.value) {
        case 'symbol':
          aValue = a.symbol
          bValue = b.symbol
          break
        case 'baseVolume':
          aValue = a.ticker?.baseVolume || 0
          bValue = b.ticker?.baseVolume || 0
          break
        case 'quoteVolume':
          aValue = a.ticker?.quoteVolume || 0
          bValue = b.ticker?.quoteVolume || 0
          break
        case 'usdtVolume':
          aValue = a.usdtVolume || 0
          bValue = b.usdtVolume || 0
          break
        case 'change':
          aValue = a.ticker?.change || 0
          bValue = b.ticker?.change || 0
          break
        case 'percentage':
          aValue = a.ticker?.percentage || 0
          bValue = b.ticker?.percentage || 0
          break
        case 'last':
          aValue = a.ticker?.last || 0
          bValue = b.ticker?.last || 0
          break
        default:
          return 0
      }

      if (typeof aValue === 'string' && typeof bValue === 'string') {
        const comparison = aValue.localeCompare(bValue)
        return sortDirection.value === 'asc' ? comparison : -comparison
      } else {
        const comparison = (aValue || 0) - (bValue || 0)
        return sortDirection.value === 'asc' ? comparison : -comparison
      }
    })

    return result
  })

  // Actions
  const fetchExchanges = async () => {
    loading.value.exchanges = true
    errors.value.exchanges = null
    
    try {
      const response = await ccxtService.getAllExchanges()
      if (response.success) {
        exchanges.value = response.data
      } else {
        errors.value.exchanges = response.error || 'Failed to fetch exchanges'
      }
    } catch (error) {
      errors.value.exchanges = error instanceof Error ? error.message : 'Unknown error'
    } finally {
      loading.value.exchanges = false
    }
  }

  const fetchSymbols = async (exchangeId?: string) => {
    loading.value.symbols = true
    errors.value.symbols = null
    
    try {
      const response = exchangeId 
        ? await ccxtService.getExchangeSymbols(exchangeId)
        : await ccxtService.getAllSymbols()
        
      if (response.success) {
        symbols.value = response.data
      } else {
        errors.value.symbols = response.error || 'Failed to fetch symbols'
      }
    } catch (error) {
      errors.value.symbols = error instanceof Error ? error.message : 'Unknown error'
    } finally {
      loading.value.symbols = false
    }
  }

  const fetchTickers = async (symbolRequests: Array<{exchangeId: string, symbol: string}>) => {
    loading.value.tickers = true
    errors.value.tickers = null
    
    try {
      const response = await ccxtService.getMultipleTickers(symbolRequests)
      if (response.success) {
        response.data.forEach(ticker => {
          tickers.value.set(`${ticker.symbol}`, ticker)
        })
      } else {
        errors.value.tickers = response.error || 'Failed to fetch tickers'
      }
    } catch (error) {
      errors.value.tickers = error instanceof Error ? error.message : 'Unknown error'
    } finally {
      loading.value.tickers = false
    }
  }

  const updateFilters = (newFilters: Partial<FilterOptions>) => {
    filters.value = { ...filters.value, ...newFilters }
  }

  const updateSort = (option: SortOption, direction?: SortDirection) => {
    sortBy.value = option
    if (direction) {
      sortDirection.value = direction
    } else {
      // 如果是同一个排序字段，切换方向
      if (sortBy.value === option) {
        sortDirection.value = sortDirection.value === 'asc' ? 'desc' : 'asc'
      } else {
        sortDirection.value = 'asc'
      }
    }
  }

  const clearErrors = () => {
    errors.value = {
      exchanges: null,
      symbols: null,
      tickers: null
    }
  }

  return {
    // 状态
    exchanges,
    symbols,
    tickers,
    loading,
    errors,
    filters,
    sortBy,
    sortDirection,
    
    // 计算属性
    filteredExchanges,
    sortedExchanges,
    filteredSymbols,
    sortedSymbols,
    
    // Actions
    fetchExchanges,
    fetchSymbols,
    fetchTickers,
    updateFilters,
    updateSort,
    clearErrors
  }
})
