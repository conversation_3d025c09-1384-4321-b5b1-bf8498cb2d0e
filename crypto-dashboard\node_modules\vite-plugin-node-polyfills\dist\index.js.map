{"version": 3, "file": "index.js", "sources": ["../src/utils.ts", "../src/index.ts"], "sourcesContent": ["import type { BooleanOrBuildTarget, ModuleName, ModuleNameWithoutNodePrefix } from './index'\n\nexport const compareModuleNames = (moduleA: ModuleName, moduleB: ModuleName) => {\n  return withoutNodeProtocol(moduleA) === withoutNodeProtocol(moduleB)\n}\n\nexport const isEnabled = (value: BooleanOrBuildTarget, mode: 'build' | 'dev') => {\n  if (!value) return false\n  if (value === true) return true\n\n  return value === mode\n}\n\nexport const isNodeProtocolImport = (name: string) => {\n  return name.startsWith('node:')\n}\n\nexport const toRegExp = (text: string) => {\n  // https://developer.mozilla.org/en-US/docs/Web/JavaScript/Guide/Regular_Expressions#escaping\n  const escapedText = text.replace(/[.*+?^${}()|[\\]\\\\]/g, '\\\\$&')\n\n  return new RegExp(`^${escapedText}$`)\n}\n\nexport const withoutNodeProtocol = (name: ModuleName): ModuleNameWithoutNodePrefix => {\n  return name.replace(/^node:/, '') as ModuleNameWithoutNodePrefix\n}\n", "import { createRequire } from 'node:module'\nimport inject from '@rollup/plugin-inject'\nimport stdLibBrowser from 'node-stdlib-browser'\nimport { handleCircularDependancyWarning } from 'node-stdlib-browser/helpers/rollup/plugin'\nimport esbuildPlugin from 'node-stdlib-browser/helpers/esbuild/plugin'\nimport type { Plugin } from 'vite'\nimport { compareModuleNames, isEnabled, isNodeProtocolImport, toRegExp, withoutNodeProtocol } from './utils'\n\nexport type BuildTarget = 'build' | 'dev'\nexport type BooleanOrBuildTarget = boolean | BuildTarget\nexport type ModuleName = keyof typeof stdLibBrowser\nexport type ModuleNameWithoutNodePrefix<T = ModuleName> = T extends `node:${infer P}` ? P : never\n\nexport type PolyfillOptions = {\n  /**\n   * Includes specific modules. If empty, includes all modules\n   * @example\n   *\n   * ```ts\n   * nodePolyfills({\n   *   include: ['fs', 'path'],\n   * })\n   * ```\n  */\n  include?: ModuleNameWithoutNodePrefix[],\n  /**\n   * @example\n   *\n   * ```ts\n   * nodePolyfills({\n   *   exclude: ['fs', 'path'],\n   * })\n   * ```\n   */\n  exclude?: ModuleNameWithoutNodePrefix[],\n  /**\n   * Specify whether specific globals should be polyfilled.\n   *\n   * @example\n   *\n   * ```ts\n   * nodePolyfills({\n   *   globals: {\n   *     Buffer: false,\n   *     global: true,\n   *     process: 'build',\n   *   },\n   * })\n   * ```\n   */\n  globals?: {\n    Buffer?: BooleanOrBuildTarget,\n    global?: BooleanOrBuildTarget,\n    process?: BooleanOrBuildTarget,\n  },\n  /**\n   * Specify alternative modules to use in place of the default polyfills.\n   *\n   * @example\n   *\n   * ```ts\n   * nodePolyfills({\n   *   overrides: {\n   *     fs: 'memfs',\n   *   },\n   * })\n   * ```\n   */\n  overrides?: { [Key in ModuleNameWithoutNodePrefix]?: string },\n  /**\n   * Specify whether the Node protocol version of an import (e.g. `node:buffer`) should be polyfilled too.\n   *\n   * @default true\n   */\n  protocolImports?: boolean,\n}\n\nexport type PolyfillOptionsResolved = {\n  include: ModuleNameWithoutNodePrefix[],\n  exclude: ModuleNameWithoutNodePrefix[],\n  globals: {\n    Buffer: BooleanOrBuildTarget,\n    global: BooleanOrBuildTarget,\n    process: BooleanOrBuildTarget,\n  },\n  overrides: { [Key in ModuleNameWithoutNodePrefix]?: string },\n  protocolImports: boolean,\n}\n\nconst globalShimBanners = {\n  buffer: [\n    `import __buffer_polyfill from 'vite-plugin-node-polyfills/shims/buffer'`,\n    `globalThis.Buffer = globalThis.Buffer || __buffer_polyfill`,\n  ],\n  global: [\n    `import __global_polyfill from 'vite-plugin-node-polyfills/shims/global'`,\n    `globalThis.global = globalThis.global || __global_polyfill`,\n  ],\n  process: [\n    `import __process_polyfill from 'vite-plugin-node-polyfills/shims/process'`,\n    `globalThis.process = globalThis.process || __process_polyfill`,\n  ],\n}\n\n/**\n * Returns a Vite plugin to polyfill Node's Core Modules for browser environments. Supports `node:` protocol imports.\n *\n * @example\n *\n * ```ts\n * // vite.config.ts\n * import { defineConfig } from 'vite'\n * import { nodePolyfills } from 'vite-plugin-node-polyfills'\n *\n * export default defineConfig({\n *   plugins: [\n *     nodePolyfills({\n *       // Specific modules that should not be polyfilled.\n *       exclude: [],\n *       // Whether to polyfill specific globals.\n *       globals: {\n *         Buffer: true, // can also be 'build', 'dev', or false\n *         global: true,\n *         process: true,\n *       },\n *       // Whether to polyfill `node:` protocol imports.\n *       protocolImports: true,\n *     }),\n *   ],\n * })\n * ```\n */\nexport const nodePolyfills = (options: PolyfillOptions = {}): Plugin => {\n  const optionsResolved: PolyfillOptionsResolved = {\n    include: [],\n    exclude: [],\n    overrides: {},\n    protocolImports: true,\n    ...options,\n    globals: {\n      Buffer: true,\n      global: true,\n      process: true,\n      ...options.globals,\n    },\n  }\n\n  const isExcluded = (moduleName: ModuleName) => {\n    if (optionsResolved.include.length > 0) {\n      return !optionsResolved.include.some((includedName) => compareModuleNames(moduleName, includedName))\n    }\n\n    return optionsResolved.exclude.some((excludedName) => compareModuleNames(moduleName, excludedName))\n  }\n\n  const toOverride = (name: ModuleNameWithoutNodePrefix): string | void => {\n    if (isEnabled(optionsResolved.globals.Buffer, 'dev') && /^buffer$/.test(name)) {\n      return 'vite-plugin-node-polyfills/shims/buffer'\n    }\n\n    if (isEnabled(optionsResolved.globals.global, 'dev') && /^global$/.test(name)) {\n      return 'vite-plugin-node-polyfills/shims/global'\n    }\n\n    if (isEnabled(optionsResolved.globals.process, 'dev') && /^process$/.test(name)) {\n      return 'vite-plugin-node-polyfills/shims/process'\n    }\n\n    if (name in optionsResolved.overrides) {\n      return optionsResolved.overrides[name]\n    }\n  }\n\n  const polyfills = (Object.entries(stdLibBrowser) as Array<[ModuleName, string]>).reduce<Record<ModuleName, string>>((included, [name, value]) => {\n    if (!optionsResolved.protocolImports) {\n      if (isNodeProtocolImport(name)) {\n        return included\n      }\n    }\n\n    if (!isExcluded(name)) {\n      included[name] = toOverride(withoutNodeProtocol(name)) || value\n    }\n\n    return included\n  }, {} as Record<ModuleName, string>)\n\n  const require = createRequire(import.meta.url)\n  const globalShimPaths = [\n    ...((isEnabled(optionsResolved.globals.Buffer, 'dev')) ? [require.resolve('vite-plugin-node-polyfills/shims/buffer')] : []),\n    ...((isEnabled(optionsResolved.globals.global, 'dev')) ? [require.resolve('vite-plugin-node-polyfills/shims/global')] : []),\n    ...((isEnabled(optionsResolved.globals.process, 'dev')) ? [require.resolve('vite-plugin-node-polyfills/shims/process')] : []),\n  ]\n\n  const globalShimsBanner = [\n    ...((isEnabled(optionsResolved.globals.Buffer, 'dev')) ? globalShimBanners.buffer : []),\n    ...((isEnabled(optionsResolved.globals.global, 'dev')) ? globalShimBanners.global : []),\n    ...((isEnabled(optionsResolved.globals.process, 'dev')) ? globalShimBanners.process : []),\n    ``,\n  ].join('\\n')\n\n  return {\n    name: 'vite-plugin-node-polyfills',\n    config(config, env) {\n      const isDev = env.command === 'serve'\n      // @ts-expect-error - this.meta.rolldownVersion only exists with rolldown-vite 7+\n      const isRolldownVite = !!this?.meta?.rolldownVersion\n\n      // https://github.com/niksy/node-stdlib-browser/blob/3e7cd7f3d115ac5c4593b550e7d8c4a82a0d4ac4/README.md?plain=1#L203-L209\n      const defines = {\n        ...((isDev && isEnabled(optionsResolved.globals.Buffer, 'dev')) ? { Buffer: 'Buffer' } : {}),\n        ...((isDev && isEnabled(optionsResolved.globals.global, 'dev')) ? { global: 'global' } : {}),\n        ...((isDev && isEnabled(optionsResolved.globals.process, 'dev')) ? { process: 'process' } : {}),\n      }\n\n      const shimsToInject = {\n        // https://github.com/niksy/node-stdlib-browser/blob/3e7cd7f3d115ac5c4593b550e7d8c4a82a0d4ac4/README.md#vite\n        ...(isEnabled(optionsResolved.globals.Buffer, 'build') ? { Buffer: 'vite-plugin-node-polyfills/shims/buffer' } : {}),\n        ...(isEnabled(optionsResolved.globals.global, 'build') ? { global: 'vite-plugin-node-polyfills/shims/global' } : {}),\n        ...(isEnabled(optionsResolved.globals.process, 'build') ? { process: 'vite-plugin-node-polyfills/shims/process' } : {}),\n      }\n\n      return {\n        build: {\n          rollupOptions: {\n            onwarn: (warning, rollupWarn) => {\n              handleCircularDependancyWarning(warning, () => {\n                if (config.build?.rollupOptions?.onwarn) {\n                  return config.build.rollupOptions.onwarn(warning, rollupWarn)\n                }\n\n                rollupWarn(warning)\n              })\n            },\n            ...Object.keys(shimsToInject).length > 0\n              ? isRolldownVite\n                ? { inject: shimsToInject }\n                : { plugins: [inject(shimsToInject)] }\n              : {},\n          },\n        },\n        esbuild: {\n          // In dev, the global polyfills need to be injected as a banner in order for isolated scripts (such as Vue SFCs) to have access to them.\n          banner: isDev ? globalShimsBanner : undefined,\n        },\n        optimizeDeps: {\n          exclude: [\n            ...globalShimPaths,\n          ],\n          ...isRolldownVite\n            ? {\n                rollupOptions: {\n                  define: defines,\n                  resolve: {\n                    // https://github.com/niksy/node-stdlib-browser/blob/3e7cd7f3d115ac5c4593b550e7d8c4a82a0d4ac4/README.md?plain=1#L150\n                    alias: {\n                      ...polyfills,\n                    },\n                  },\n                  plugins: [\n                    {\n                      name: 'vite-plugin-node-polyfills:optimizer',\n                      banner: isDev ? globalShimsBanner : undefined,\n                    },\n                  ],\n                },\n              }\n            : {\n                esbuildOptions: {\n                  banner: isDev ? { js: globalShimsBanner } : undefined,\n                  define: defines,\n                  inject: [\n                    ...globalShimPaths,\n                  ],\n                  plugins: [\n                    esbuildPlugin(polyfills),\n                    // Supress the 'injected path \"...\" cannot be marked as external' error in Vite 4 (emitted by esbuild).\n                    // https://github.com/evanw/esbuild/blob/edede3c49ad6adddc6ea5b3c78c6ea7507e03020/internal/bundler/bundler.go#L1469\n                    {\n                      name: 'vite-plugin-node-polyfills-shims-resolver',\n                      setup(build) {\n                        for (const globalShimPath of globalShimPaths) {\n                          const globalShimsFilter = toRegExp(globalShimPath)\n\n                          // https://esbuild.github.io/plugins/#on-resolve\n                          build.onResolve({ filter: globalShimsFilter }, () => {\n                            return {\n                              // https://github.com/evanw/esbuild/blob/edede3c49ad6adddc6ea5b3c78c6ea7507e03020/internal/bundler/bundler.go#L1468\n                              external: false,\n                              path: globalShimPath,\n                            }\n                          })\n                        }\n                      },\n                    },\n                  ],\n                },\n              },\n        },\n        resolve: {\n          // https://github.com/niksy/node-stdlib-browser/blob/3e7cd7f3d115ac5c4593b550e7d8c4a82a0d4ac4/README.md?plain=1#L150\n          alias: {\n            ...polyfills,\n          },\n        },\n      }\n    },\n  }\n}\n"], "names": ["compareModuleNames", "moduleA", "moduleB", "withoutNodeProtocol", "isEnabled", "value", "mode", "isNodeProtocolImport", "name", "toRegExp", "text", "escapedText", "globalShimBanners", "nodePolyfills", "options", "optionsResolved", "isExcluded", "moduleName", "<PERSON><PERSON><PERSON>", "excludedName", "toOverride", "polyfills", "stdLibBrowser", "included", "require", "createRequire", "globalShimPaths", "globalShimsBanner", "config", "env", "isDev", "isRolldownVite", "defines", "shimsToInject", "warning", "rollupWarn", "handleCircularDependancyWarning", "inject", "esbuildPlugin", "build", "globalShimPath", "globalShimsFilter"], "mappings": ";;;;;AAEa,MAAAA,IAAqB,CAACC,GAAqBC,MAC/CC,EAAoBF,CAAO,MAAME,EAAoBD,CAAO,GAGxDE,IAAY,CAACC,GAA6BC,MAChDD,IACDA,MAAU,KAAa,KAEpBA,MAAUC,IAHE,IAMRC,IAAuB,CAACC,MAC5BA,EAAK,WAAW,OAAO,GAGnBC,IAAW,CAACC,MAAiB;AAExC,QAAMC,IAAcD,EAAK,QAAQ,uBAAuB,MAAM;AAE9D,SAAO,IAAI,OAAO,IAAIC,CAAW,GAAG;AACtC,GAEaR,IAAsB,CAACK,MAC3BA,EAAK,QAAQ,UAAU,EAAE,GCgE5BI,IAAoB;AAAA,EACxB,QAAQ;AAAA,IACN;AAAA,IACA;AAAA,EACF;AAAA,EACA,QAAQ;AAAA,IACN;AAAA,IACA;AAAA,EACF;AAAA,EACA,SAAS;AAAA,IACP;AAAA,IACA;AAAA,EACF;AACF,GA8BaC,IAAgB,CAACC,IAA2B,OAAe;AACtE,QAAMC,IAA2C;AAAA,IAC/C,SAAS,CAAC;AAAA,IACV,SAAS,CAAC;AAAA,IACV,WAAW,CAAC;AAAA,IACZ,iBAAiB;AAAA,IACjB,GAAGD;AAAA,IACH,SAAS;AAAA,MACP,QAAQ;AAAA,MACR,QAAQ;AAAA,MACR,SAAS;AAAA,MACT,GAAGA,EAAQ;AAAA,IACb;AAAA,EAAA,GAGIE,IAAa,CAACC,MACdF,EAAgB,QAAQ,SAAS,IAC5B,CAACA,EAAgB,QAAQ,KAAK,CAACG,MAAiBlB,EAAmBiB,GAAYC,CAAY,CAAC,IAG9FH,EAAgB,QAAQ,KAAK,CAACI,MAAiBnB,EAAmBiB,GAAYE,CAAY,CAAC,GAG9FC,IAAa,CAACZ,MAAqD;AACnE,QAAAJ,EAAUW,EAAgB,QAAQ,QAAQ,KAAK,KAAK,WAAW,KAAKP,CAAI;AACnE,aAAA;AAGL,QAAAJ,EAAUW,EAAgB,QAAQ,QAAQ,KAAK,KAAK,WAAW,KAAKP,CAAI;AACnE,aAAA;AAGL,QAAAJ,EAAUW,EAAgB,QAAQ,SAAS,KAAK,KAAK,YAAY,KAAKP,CAAI;AACrE,aAAA;AAGL,QAAAA,KAAQO,EAAgB;AACnB,aAAAA,EAAgB,UAAUP,CAAI;AAAA,EACvC,GAGIa,IAAa,OAAO,QAAQC,CAAa,EAAkC,OAAmC,CAACC,GAAU,CAACf,GAAMH,CAAK,OACrI,CAACU,EAAgB,mBACfR,EAAqBC,CAAI,KAK1BQ,EAAWR,CAAI,MAClBe,EAASf,CAAI,IAAIY,EAAWjB,EAAoBK,CAAI,CAAC,KAAKH,IAGrDkB,IACN,CAAgC,CAAA,GAE7BC,IAAUC,EAAc,YAAY,GAAG,GACvCC,IAAkB;AAAA,IACtB,GAAKtB,EAAUW,EAAgB,QAAQ,QAAQ,KAAK,IAAK,CAACS,EAAQ,QAAQ,yCAAyC,CAAC,IAAI,CAAC;AAAA,IACzH,GAAKpB,EAAUW,EAAgB,QAAQ,QAAQ,KAAK,IAAK,CAACS,EAAQ,QAAQ,yCAAyC,CAAC,IAAI,CAAC;AAAA,IACzH,GAAKpB,EAAUW,EAAgB,QAAQ,SAAS,KAAK,IAAK,CAACS,EAAQ,QAAQ,0CAA0C,CAAC,IAAI,CAAC;AAAA,EAAA,GAGvHG,IAAoB;AAAA,IACxB,GAAKvB,EAAUW,EAAgB,QAAQ,QAAQ,KAAK,IAAKH,EAAkB,SAAS,CAAC;AAAA,IACrF,GAAKR,EAAUW,EAAgB,QAAQ,QAAQ,KAAK,IAAKH,EAAkB,SAAS,CAAC;AAAA,IACrF,GAAKR,EAAUW,EAAgB,QAAQ,SAAS,KAAK,IAAKH,EAAkB,UAAU,CAAC;AAAA,IACvF;AAAA,EAAA,EACA,KAAK;AAAA,CAAI;AAEJ,SAAA;AAAA,IACL,MAAM;AAAA,IACN,OAAOgB,GAAQC,GAAK;AACZ,YAAAC,IAAQD,EAAI,YAAY,SAExBE,IAAiB,CAAC,CAAC,MAAM,MAAM,iBAG/BC,IAAU;AAAA,QACd,GAAKF,KAAS1B,EAAUW,EAAgB,QAAQ,QAAQ,KAAK,IAAK,EAAE,QAAQ,SAAS,IAAI,CAAC;AAAA,QAC1F,GAAKe,KAAS1B,EAAUW,EAAgB,QAAQ,QAAQ,KAAK,IAAK,EAAE,QAAQ,SAAS,IAAI,CAAC;AAAA,QAC1F,GAAKe,KAAS1B,EAAUW,EAAgB,QAAQ,SAAS,KAAK,IAAK,EAAE,SAAS,UAAU,IAAI,CAAC;AAAA,MAAA,GAGzFkB,IAAgB;AAAA;AAAA,QAEpB,GAAI7B,EAAUW,EAAgB,QAAQ,QAAQ,OAAO,IAAI,EAAE,QAAQ,0CAA0C,IAAI,CAAC;AAAA,QAClH,GAAIX,EAAUW,EAAgB,QAAQ,QAAQ,OAAO,IAAI,EAAE,QAAQ,0CAA0C,IAAI,CAAC;AAAA,QAClH,GAAIX,EAAUW,EAAgB,QAAQ,SAAS,OAAO,IAAI,EAAE,SAAS,2CAA2C,IAAI,CAAC;AAAA,MAAA;AAGhH,aAAA;AAAA,QACL,OAAO;AAAA,UACL,eAAe;AAAA,YACb,QAAQ,CAACmB,GAASC,MAAe;AAC/B,cAAAC,EAAgCF,GAAS,MAAM;AACzC,oBAAAN,EAAO,OAAO,eAAe;AAC/B,yBAAOA,EAAO,MAAM,cAAc,OAAOM,GAASC,CAAU;AAG9D,gBAAAA,EAAWD,CAAO;AAAA,cAAA,CACnB;AAAA,YACH;AAAA,YACA,GAAG,OAAO,KAAKD,CAAa,EAAE,SAAS,IACnCF,IACE,EAAE,QAAQE,MACV,EAAE,SAAS,CAACI,EAAOJ,CAAa,CAAC,EAAA,IACnC,CAAC;AAAA,UACP;AAAA,QACF;AAAA,QACA,SAAS;AAAA;AAAA,UAEP,QAAQH,IAAQH,IAAoB;AAAA,QACtC;AAAA,QACA,cAAc;AAAA,UACZ,SAAS;AAAA,YACP,GAAGD;AAAA,UACL;AAAA,UACA,GAAGK,IACC;AAAA,YACE,eAAe;AAAA,cACb,QAAQC;AAAA,cACR,SAAS;AAAA;AAAA,gBAEP,OAAO;AAAA,kBACL,GAAGX;AAAA,gBACL;AAAA,cACF;AAAA,cACA,SAAS;AAAA,gBACP;AAAA,kBACE,MAAM;AAAA,kBACN,QAAQS,IAAQH,IAAoB;AAAA,gBACtC;AAAA,cACF;AAAA,YACF;AAAA,UAAA,IAEF;AAAA,YACE,gBAAgB;AAAA,cACd,QAAQG,IAAQ,EAAE,IAAIH,MAAsB;AAAA,cAC5C,QAAQK;AAAA,cACR,QAAQ;AAAA,gBACN,GAAGN;AAAA,cACL;AAAA,cACA,SAAS;AAAA,gBACPY,EAAcjB,CAAS;AAAA;AAAA;AAAA,gBAGvB;AAAA,kBACE,MAAM;AAAA,kBACN,MAAMkB,GAAO;AACX,+BAAWC,KAAkBd,GAAiB;AACtC,4BAAAe,IAAoBhC,EAAS+B,CAAc;AAGjD,sBAAAD,EAAM,UAAU,EAAE,QAAQE,KAAqB,OACtC;AAAA;AAAA,wBAEL,UAAU;AAAA,wBACV,MAAMD;AAAA,sBAAA,EAET;AAAA,oBACH;AAAA,kBACF;AAAA,gBACF;AAAA,cACF;AAAA,YACF;AAAA,UACF;AAAA,QACN;AAAA,QACA,SAAS;AAAA;AAAA,UAEP,OAAO;AAAA,YACL,GAAGnB;AAAA,UACL;AAAA,QACF;AAAA,MAAA;AAAA,IAEJ;AAAA,EAAA;AAEJ;"}