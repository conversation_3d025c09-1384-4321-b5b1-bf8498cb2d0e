import type { BooleanOrBuildTarget, ModuleName, ModuleNameWithoutNodePrefix } from './index';
export declare const compareModuleNames: (moduleA: ModuleName, moduleB: ModuleName) => boolean;
export declare const isEnabled: (value: BooleanOrBuildTarget, mode: 'build' | 'dev') => boolean;
export declare const isNodeProtocolImport: (name: string) => boolean;
export declare const toRegExp: (text: string) => RegExp;
export declare const withoutNodeProtocol: (name: ModuleName) => ModuleNameWithoutNodePrefix;
