{"version": 3, "file": "process.js", "sources": ["../../mock/process.js"], "sourcesContent": ["/* globals unknown */\n\nimport path from 'path';\n\nfunction noop() {}\n/**\n * @param {unknown[]} arguments_\n */\nfunction nextTick(...arguments_) {\n\tconst [function_] = arguments_;\n\targuments_.shift();\n\tsetTimeout(function () {\n\t\tif (typeof function_ === 'function') {\n\t\t\tfunction_.apply(null, arguments_);\n\t\t}\n\t}, 0);\n}\n\n/**\n * @param {unknown} name\n */\nfunction binding(name) {\n\tthrow new Error('No such module. (Possibly not yet loaded)');\n}\n\nconst features = {};\nconst platformName = 'browser';\nconst pid = 1;\nconst browser = true;\nconst environment = {};\n/** @type {string[]} */\nconst argv = [];\n\nlet cwd = '/';\nfunction getCwd() {\n\treturn cwd;\n}\n/**\n * @param {string} dir\n */\nfunction getChdir(dir) {\n\tcwd = path.resolve(dir, cwd);\n}\n\nexport {\n\tfeatures,\n\tnextTick,\n\tpid,\n\tbrowser,\n\tenvironment as env,\n\targv,\n\tbinding,\n\tgetCwd as cwd,\n\tgetChdir as chdir,\n\tnoop as exit,\n\tnoop as kill,\n\tnoop as umask,\n\tnoop as dlopen,\n\tnoop as uptime,\n\tnoop as memoryUsage,\n\tnoop as uvCounters,\n\tplatformName as platform,\n\tplatformName as arch,\n\tplatformName as execPath,\n\tplatformName as title\n};\n"], "names": ["noop", "nextTick", "_len", "arguments", "length", "arguments_", "Array", "_key", "function_", "shift", "setTimeout", "apply", "binding", "name", "Error", "features", "platformName", "pid", "browser", "environment", "argv", "cwd", "getCwd", "getChdir", "dir", "path", "resolve"], "mappings": ";;;;;;;;;;AAAA;AAIA,SAASA,IAAIA,GAAG,EAAC;AACjB;AACA;AACA;AACA,SAASC,QAAQA,GAAgB;AAAA,EAAA,KAAA,IAAAC,IAAA,GAAAC,SAAA,CAAAC,MAAA,EAAZC,UAAU,GAAAC,IAAAA,KAAA,CAAAJ,IAAA,GAAAK,IAAA,GAAA,CAAA,EAAAA,IAAA,GAAAL,IAAA,EAAAK,IAAA,EAAA,EAAA;AAAVF,IAAAA,UAAU,CAAAE,IAAA,CAAAJ,GAAAA,SAAA,CAAAI,IAAA,CAAA,CAAA;AAAA,GAAA;EAC9B,IAAOC,SAAS,GAAIH,UAAU,CAAA,CAAA,CAAA,CAAA;EAC9BA,UAAU,CAACI,KAAK,EAAE,CAAA;AAClBC,EAAAA,UAAU,CAAC,YAAY;AACtB,IAAA,IAAI,OAAOF,SAAS,KAAK,UAAU,EAAE;AACpCA,MAAAA,SAAS,CAACG,KAAK,CAAC,IAAI,EAAEN,UAAU,CAAC,CAAA;AAClC,KAAA;GACA,EAAE,CAAC,CAAC,CAAA;AACN,CAAA;;AAEA;AACA;AACA;AACA,SAASO,OAAOA,CAACC,IAAI,EAAE;AACtB,EAAA,MAAM,IAAIC,KAAK,CAAC,2CAA2C,CAAC,CAAA;AAC7D,CAAA;AAEMC,IAAAA,QAAQ,GAAG,GAAE;AACbC,IAAAA,YAAY,GAAG,UAAS;AACxBC,IAAAA,GAAG,GAAG,EAAC;AACPC,IAAAA,OAAO,GAAG,KAAI;AACdC,IAAAA,WAAW,GAAG,GAAE;AACtB;AACMC,IAAAA,IAAI,GAAG,GAAE;AAEf,IAAIC,GAAG,GAAG,GAAG,CAAA;AACb,SAASC,MAAMA,GAAG;AACjB,EAAA,OAAOD,GAAG,CAAA;AACX,CAAA;AACA;AACA;AACA;AACA,SAASE,QAAQA,CAACC,GAAG,EAAE;EACtBH,GAAG,GAAGI,wBAAI,CAACC,OAAO,CAACF,GAAG,EAAEH,GAAG,CAAC,CAAA;AAC7B;;;;;;;;;;;;;;;;;;;;;;;"}